# DIYPC - AI-Powered PC Building Assistant

DIYPC is an AI-powered web application that helps users build custom PCs tailored to their specific needs, budget, and preferences. The application leverages Firebase services and Vertex AI to provide intelligent recommendations and interactive assistance.

## Features

- **AI-Powered Build Recommendations**: Get personalized PC build recommendations based on your use case, budget, and specific requirements.
- **Interactive Chat Interface**: Chat with our AI assistant to refine your build and get answers to your PC building questions.
- **Component Selection**: Review and customize your build with detailed component information.
- **Build Management**: Save, share, and export your PC builds.
- **Responsive Design**: Fully responsive UI that works on desktop and mobile devices.

## Technology Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, shadcn/ui
- **State Management**: Zustand
- **Backend Services**: Firebase (Authentication, Firestore, Analytics)
- **AI**: Vertex AI (Gemini 2.5 Flash)
- **Animation**: Framer Motion

## Architecture

The application follows a modular architecture with clear separation of concerns:

### Services

- **Firebase Services**: Authentication, Firestore, Analytics
- **Vertex AI Service**: AI-powered recommendations and chat

### State Management

- **Build Store**: Manages PC build state, recommendations, and chat
- **Auth Store**: Manages user authentication state

### UI Components

- **Layout Components**: Main layout, navigation
- **Build Components**: Component selector, build summary
- **Chat Components**: Chat interface, messages

## Implementation Details

### Firebase Integration

The application uses Firebase for:

- **Authentication**: User sign-up, sign-in, and profile management
- **Firestore**: Storing and retrieving PC builds
- **Analytics**: Tracking user interactions and events

### Vertex AI Integration

The application uses Vertex AI for:

- **Build Recommendations**: Generating PC build recommendations based on user requirements
- **Chat Interface**: Providing interactive assistance and answering user questions
- **Component Explanations**: Explaining component choices and compatibility

### State Management with Zustand

The application uses Zustand for state management:

- **Build Store**: Manages PC build state, recommendations, and chat
- **Auth Store**: Manages user authentication state

### Structured Output

The application uses structured output (JSON) for:

- **Build Recommendations**: Ensuring consistent and structured build recommendations
- **Component Information**: Providing detailed component information

### Multi-turn Chat

The application uses multi-turn chat for:

- **Requirements Gathering**: Understanding user requirements through conversation
- **Build Refinement**: Refining build recommendations based on user feedback
- **Q&A**: Answering user questions about PC building

## Getting Started

### Prerequisites

- Node.js 18.x or later
- Firebase project with Firestore, Authentication, and Analytics enabled
- Vertex AI API access

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/diypc.git
   cd diypc
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   Create a `.env.local` file with the following variables:
   ```
   NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-auth-domain
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-storage-bucket
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
   NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
   NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id
   ```

4. Run the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

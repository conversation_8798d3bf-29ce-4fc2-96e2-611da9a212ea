'use client';

import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ChatMessage } from '@/types/pc-build';
import { motion } from 'framer-motion';
import { Send } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';

interface ChatInputProps {
  onSend: (message: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  disabled?: boolean;
  autoFocus?: boolean;
  suggestedQuestions?: string[];
}

export function ChatInput({
  onSend,
  isLoading = false,
  placeholder = 'Tell me what kind of PC you need, including your budget and use case (e.g., "I need a gaming PC for $1500 that can run modern games at high settings")...',
  disabled = false,
  autoFocus = false,
  suggestedQuestions = []
}: Readonly<ChatInputProps>) {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  const handleSend = () => {
    if (message.trim() && !isLoading && !disabled) {
      onSend(message);
      setMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="space-y-4">
      {suggestedQuestions.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {suggestedQuestions.map((question, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Button
                variant="outline"
                size="sm"
                onClick={() => onSend(question)}
                disabled={isLoading || disabled}
                className="text-xs"
              >
                {question}
              </Button>
            </motion.div>
          ))}
        </div>
      )}

      <div className="flex gap-2">
        <Textarea
          ref={textareaRef}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={isLoading || disabled}
          className="min-h-[60px] resize-none"
          rows={1}
        />
        <Button
          onClick={handleSend}
          disabled={!message.trim() || isLoading || disabled}
          size="icon"
          className="h-[60px] w-[60px] shrink-0"
        >
          <Send className="h-5 w-5" />
        </Button>
      </div>
    </div>
  );
}

interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  suggestedQuestions?: string[];
}

export function ChatInterface({
  onSendMessage,
  isLoading = false,
  disabled = false,
  suggestedQuestions = [],
}: Readonly<ChatInterfaceProps>) {
  return (
    <div className="mt-4">
      <ChatInput
        onSend={onSendMessage}
        isLoading={isLoading}
        disabled={disabled}
        suggestedQuestions={suggestedQuestions}
      />
    </div>
  );
}

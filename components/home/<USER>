'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Particles } from '@/components/ui/particles';
import { ArrowRight, Cpu, Zap, Shield } from 'lucide-react';

export function HeroSection() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  const features = [
    {
      icon: Cpu,
      title: 'AI-Powered Recommendations',
      description: 'Get personalized component recommendations based on your needs and budget.',
    },
    {
      icon: Shield,
      title: 'Compatibility Checking',
      description: 'Ensure all your components work together perfectly with automatic compatibility checks.',
    },
    {
      icon: Zap,
      title: 'Performance Insights',
      description: 'Understand how your build will perform for your specific use cases.',
    },
  ];

  return (
    <section className="relative overflow-hidden py-20 md:py-32">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-background to-background/50 pointer-events-none" />

      {/* Particles background */}
      <div className="absolute inset-0">
        <Particles
          className="absolute inset-0"
          quantity={100}
          staticity={30}
          color="oklch(var(--primary))"
          ease={50}
          size={0.8}
        />
      </div>

      <div className="container max-w-7xl px-4 mx-auto relative z-10">
        <motion.div
          className="flex flex-col items-center text-center max-w-3xl mx-auto"
          variants={container}
          initial="hidden"
          animate="show"
        >
          <motion.h1
            className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight"
            variants={item}
          >
            Build Your Perfect PC with{' '}
            <span className="text-primary">AI Assistance</span>
          </motion.h1>

          <motion.p
            className="mt-6 text-lg md:text-xl text-muted-foreground max-w-2xl"
            variants={item}
          >
            DIYPC helps you create a custom PC that meets your needs and budget with AI-powered recommendations, compatibility checking, and expert guidance.
          </motion.p>

          <motion.div
            className="mt-10 flex flex-col sm:flex-row gap-4 w-full justify-center"
            variants={item}
          >
            <Button size="lg" className="group" asChild>
              <Link href="/build/new">
                Start Building
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="#features">Learn More</Link>
            </Button>
          </motion.div>
        </motion.div>

        <motion.div
          className="mt-20 grid gap-8 md:grid-cols-3"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          {features.map((feature, i) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                className="bg-card border rounded-lg p-6 shadow-sm"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 + (i * 0.1) }}
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
              >
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <Icon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
}

'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Star } from 'lucide-react';

export function TestimonialsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.2 });

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Gaming Enthusiast',
      image: '/logo.png',
      content: 'DIYPC made building my first gaming PC so much easier than I expected. The AI recommendations were spot-on for my budget and the games I play.',
      initials: '<PERSON>',
    },
    {
      name: '<PERSON>',
      role: 'Video Editor',
      image: '/logo.png',
      content: 'As a professional video editor, I needed a workstation that could handle 4K footage without breaking a sweat. DIYPC helped me build exactly what I needed.',
      initials: 'SC',
    },
    {
      name: '<PERSON>',
      role: 'Software Developer',
      image: '/logo.png',
      content: 'The compatibility checking feature saved me from making a costly mistake. The AI caught an issue I would have missed and suggested a better alternative.',
      initials: '<PERSON>',
    },
    {
      name: '<PERSON>',
      role: 'Student',
      image: '/logo.png',
      content: 'On a tight budget for my college PC, DIYPC helped me get the most performance for my money. The explanations helped me learn about PC components too!',
      initials: 'EW',
    },
    {
      name: 'David Park',
      role: '3D Artist',
      image: '/logo.png',
      content: 'The specialized recommendations for 3D rendering were impressive. My new workstation handles Blender and Maya projects with ease.',
      initials: 'DP',
    },
    {
      name: 'Olivia Martinez',
      role: 'Streamer',
      image: '/logo.png',
      content: 'DIYPC understood exactly what I needed for streaming and gaming simultaneously. My streams are now smooth and my gameplay is lag-free.',
      initials: 'OM',
    },
  ];

  return (
    <section className="py-20 bg-background" ref={ref}>
      <div className="container max-w-7xl px-4 mx-auto">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Users Say</h2>
          <p className="text-muted-foreground text-lg">
            Join thousands of satisfied PC builders who have used DIYPC to create their perfect custom computers.
          </p>
        </motion.div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial, i) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.4, delay: 0.1 * i }}
            >
              <Card className="h-full flex flex-col">
                <CardContent className="pt-6 flex-grow">
                  <div className="flex mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-primary text-primary" />
                    ))}
                  </div>
                  <p className="text-foreground">{testimonial.content}</p>
                </CardContent>
                <CardFooter className="border-t pt-4">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage src={testimonial.image} alt={testimonial.name} />
                      <AvatarFallback>{testimonial.initials}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{testimonial.name}</p>
                      <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                    </div>
                  </div>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

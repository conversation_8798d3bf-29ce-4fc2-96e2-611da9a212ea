'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import {
  Cpu,
  Shield,
  Zap,
  MessageSquare,
  Share2,
  DollarSign,
  BookOpen,
  BarChart4,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function FeaturesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.2 });

  const features = [
    {
      icon: Cpu,
      title: 'AI-Powered Recommendations',
      description: 'Get personalized component recommendations based on your specific needs, preferences, and budget constraints.',
    },
    {
      icon: Shield,
      title: 'Compatibility Checking',
      description: 'Our AI automatically verifies that all components in your build work together perfectly, preventing costly mistakes.',
    },
    {
      icon: MessageSquare,
      title: 'Interactive Chat',
      description: 'Have a natural conversation with our AI assistant to refine your build and get answers to your questions.',
    },
    {
      icon: Zap,
      title: 'Performance Insights',
      description: 'Understand how your build will perform for specific tasks like gaming, video editing, or 3D rendering.',
    },
    {
      icon: Share2,
      title: 'Save & Share Builds',
      description: 'Save your PC builds to your account and share them with friends or the community.',
    },
    {
      icon: DollarSign,
      title: 'Budget Optimization',
      description: 'Our AI helps you get the best performance possible within your specified budget.',
    },
    {
      icon: BookOpen,
      title: 'Educational Guidance',
      description: 'Learn about PC components and building as you go with explanations tailored to your knowledge level.',
    },
    {
      icon: BarChart4,
      title: 'Benchmark Comparisons',
      description: 'Compare your build against popular configurations to understand relative performance.',
    },
  ];

  const useCases = [
    {
      id: 'gaming',
      title: 'Gaming',
      description: 'Build a PC optimized for your favorite games, whether you need high FPS for competitive gaming or stunning visuals for immersive experiences.',
      specs: [
        'High-performance GPUs for smooth gameplay',
        'Fast CPUs to prevent bottlenecks',
        'Optimized cooling for extended gaming sessions',
        'RGB lighting options for gaming aesthetics',
      ],
    },
    {
      id: 'content',
      title: 'Content Creation',
      description: 'Get a workstation that handles video editing, 3D rendering, and other creative tasks with ease.',
      specs: [
        'Multi-core CPUs for faster rendering',
        'Professional-grade GPUs for creative applications',
        'High-capacity, fast storage solutions',
        'Color-accurate display recommendations',
      ],
    },
    {
      id: 'work',
      title: 'Work & Productivity',
      description: 'Build a reliable, efficient PC for professional work, multitasking, and everyday productivity.',
      specs: [
        'Balanced performance for office applications',
        'Multiple monitor support for productivity',
        'Quiet operation for office environments',
        'Energy-efficient component options',
      ],
    },
    {
      id: 'budget',
      title: 'Budget Builds',
      description: "Create an affordable PC that doesn't compromise on the performance you need.",
      specs: [
        'Cost-effective component recommendations',
        'Future upgrade paths for gradual improvements',
        'Performance optimization for essential tasks',
        'Value-focused part selection',
      ],
    },
  ];

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  return (
    <section id="features" className="py-20 bg-muted/30" ref={ref}>
      <div className="container max-w-7xl px-4 mx-auto">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Powerful Features for PC Builders</h2>
          <p className="text-muted-foreground text-lg">
            DIYPC combines AI intelligence with user-friendly tools to make PC building accessible to everyone.
          </p>
        </motion.div>

        <motion.div
          className="grid gap-6 md:grid-cols-2 lg:grid-cols-4"
          variants={container}
          initial="hidden"
          animate={isInView ? "show" : "hidden"}
        >
          {features.map((feature) => {
            const Icon = feature.icon;
            return (
              <motion.div key={feature.title} variants={item}>
                <Card className="h-full border-2 hover:border-primary/50 transition-colors">
                  <CardHeader>
                    <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                      <Icon className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle>{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">{feature.description}</CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>

        <motion.div
          className="mt-24"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center">Build for Any Purpose</h2>

          <Tabs defaultValue="gaming" className="w-full">
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 mb-8">
              {useCases.map((useCase) => (
                <TabsTrigger key={useCase.id} value={useCase.id}>
                  {useCase.title}
                </TabsTrigger>
              ))}
            </TabsList>

            {useCases.map((useCase) => (
              <TabsContent key={useCase.id} value={useCase.id} className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>{useCase.title}</CardTitle>
                    <CardDescription className="text-base">{useCase.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {useCase.specs.map((spec, i) => (
                        <motion.li
                          key={i}
                          className="flex items-start gap-2"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * i }}
                        >
                          <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mt-0.5">
                            <span className="h-2.5 w-2.5 rounded-full bg-primary" />
                          </div>
                          <span>{spec}</span>
                        </motion.li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </motion.div>
      </div>
    </section>
  );
}

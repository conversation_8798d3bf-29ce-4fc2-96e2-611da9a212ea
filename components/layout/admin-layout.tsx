'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { useAuthStore } from '@/lib/store/auth-store';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ComponentType } from '@/types/pc-build';
import { Cpu, Gpu, MemoryStick, HardDrive, Power, Box, Fan, Layers, Home, LogOut } from 'lucide-react';
import { motion } from 'framer-motion';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export const AdminLayout = ({ children }: AdminLayoutProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const { user, signOut } = useAuthStore();
  const [activeTab, setActiveTab] = useState<string>('dashboard');

  // Component types with their icons
  const componentTypes: { type: ComponentType; icon: React.ReactNode; path: string }[] = [
    { type: 'CPU', icon: <Cpu className="h-4 w-4 mr-2" />, path: '/admin/cpu' },
    { type: 'GPU', icon: <Gpu className="h-4 w-4 mr-2" />, path: '/admin/gpu' },
    { type: 'RAM', icon: <MemoryStick className="h-4 w-4 mr-2" />, path: '/admin/ram' },
    { type: 'Motherboard', icon: <Layers className="h-4 w-4 mr-2" />, path: '/admin/motherboard' },
    { type: 'SSD', icon: <HardDrive className="h-4 w-4 mr-2" />, path: '/admin/ssd' },
    { type: 'HDD', icon: <HardDrive className="h-4 w-4 mr-2" />, path: '/admin/hdd' },
    { type: 'PSU', icon: <Power className="h-4 w-4 mr-2" />, path: '/admin/psu' },
    { type: 'Case', icon: <Box className="h-4 w-4 mr-2" />, path: '/admin/case' },
    { type: 'CPU Cooler', icon: <Fan className="h-4 w-4 mr-2" />, path: '/admin/cpu-cooler' },
  ];

  // Set active tab based on pathname
  useEffect(() => {
    if (pathname === '/admin') {
      setActiveTab('dashboard');
    } else {
      const componentPath = pathname.split('/')[2];
      if (componentPath) {
        setActiveTab(componentPath);
      }
    }
  }, [pathname]);

  // Check if user is authenticated
  useEffect(() => {
    if (!user) {
      router.push('/auth/signin?redirect=/admin');
    }
  }, [user, router]);

  // Handle sign out
  const handleSignOut = async () => {
    await signOut();
    router.push('/');
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-background font-[family-name:var(--font-poppins)]">
      <header className="border-b bg-card">
        <div className="container max-w-7xl px-4 py-4 mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold mr-8">
                DIYPC Admin
              </Link>
              <nav className="hidden md:flex space-x-4">
                <Link href="/admin" className={`text-sm font-medium ${pathname === '/admin' ? 'text-primary' : 'text-muted-foreground hover:text-foreground'}`}>
                  Dashboard
                </Link>
                {componentTypes.map((component) => (
                  <Link
                    key={component.type}
                    href={component.path}
                    className={`text-sm font-medium ${pathname.startsWith(component.path) ? 'text-primary' : 'text-muted-foreground hover:text-foreground'}`}
                  >
                    {component.type}
                  </Link>
                ))}
              </nav>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" onClick={handleSignOut}>
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile navigation */}
      <div className="md:hidden border-b bg-card">
        <div className="container px-4 py-2 mx-auto overflow-x-auto">
          <Tabs value={activeTab} className="w-full" onValueChange={(value) => router.push(value === 'dashboard' ? '/admin' : `/admin/${value}`)}>
            <TabsList className="inline-flex w-auto">
              <TabsTrigger value="dashboard" className="flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Dashboard
              </TabsTrigger>
              {componentTypes.map((component) => (
                <TabsTrigger
                  key={component.type}
                  value={component.path.split('/')[2]}
                  className="flex items-center"
                >
                  {component.icon}
                  {component.type}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>
      </div>

      <motion.main
        className="flex-1"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {children}
      </motion.main>

      <footer className="border-t py-6 bg-card">
        <div className="container max-w-7xl px-4 mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} DIYPC Admin Panel
            </p>
            <div className="flex items-center mt-4 md:mt-0">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/">
                  <Home className="h-4 w-4 mr-2" />
                  Back to Site
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

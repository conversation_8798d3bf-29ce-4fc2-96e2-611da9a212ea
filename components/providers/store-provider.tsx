'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/lib/store/auth-store';
import { useBuildStore } from '@/lib/store/build-store';

interface StoreProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component to initialize stores
 */
export function StoreProvider({ children }: StoreProviderProps) {
  const initAuth = useAuthStore((state) => state.initAuth);
  const loadRecommendedBuilds = useBuildStore((state) => state.loadRecommendedBuilds);
  const user = useAuthStore((state) => state.user);
  const loadUserBuilds = useBuildStore((state) => state.loadUserBuilds);

  // Initialize auth state
  useEffect(() => {
    initAuth();
  }, [initAuth]);

  // Load recommended builds on mount
  useEffect(() => {
    loadRecommendedBuilds();
  }, [loadRecommendedBuilds]);

  // Load user builds when user is authenticated
  useEffect(() => {
    if (user) {
      loadUserBuilds(user.uid);
    }
  }, [user, loadUserBuilds]);

  return <>{children}</>;
}

export default StoreProvider;

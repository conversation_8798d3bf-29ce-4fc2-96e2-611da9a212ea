'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { PCBuild, RetailerLink } from '@/types/pc-build';
import { motion } from 'framer-motion';
import { Check, Download, Save, Share2, ShoppingCart, X } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import Amazon from '@/public/amazon.svg';
import Newegg from '@/public/newegg.svg';
import BestBuy from '@/public/best-buy.svg';
import Walmart from '@/public/walmart.svg';
import Image from 'next/image';
interface BuildSummaryProps {
  build: PCBuild;
  onSave?: () => void;
  onShare?: () => void;
  onExport?: () => void;
}

export const BuildSummary = ({ build, onSave, onShare, onExport }: BuildSummaryProps) => {
  // Function to generate retailer links (mock implementation)
  const getRetailerLinks = (componentName: string): RetailerLink[] => {
    return [
      { name: 'Amazon', url: `https://amazon.com/s?k=${encodeURIComponent(componentName)}`, icon: Amazon },
      { name: 'Newegg', url: `https://newegg.com/p/pl?d=${encodeURIComponent(componentName)}`, icon: Newegg },
      { name: 'Best Buy', url: `https://bestbuy.com/site/searchpage.jsp?st=${encodeURIComponent(componentName)}`, icon: BestBuy },
      { name: 'Walmart', url: `https://www.walmart.com/search?q=${encodeURIComponent(componentName)}`, icon: Walmart },
    ];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader>
          <CardTitle>Build Summary</CardTitle>
          <CardDescription>
            {build.description ?? `${build.useCase.charAt(0).toUpperCase() + build.useCase.slice(1)} PC build with a budget of ${build.budget}`}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-muted-foreground">Total Price</p>
              <p className="text-2xl font-bold">${build.totalPrice.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Compatibility</p>
              {build.compatibility.isCompatible ? (
                <p className="text-green-500 flex items-center">
                  <Check className="mr-1 h-4 w-4" /> Compatible
                </p>
              ) : (
                <p className="text-red-500 flex items-center">
                  <X className="mr-1 h-4 w-4" /> Issues Detected
                </p>
              )}
            </div>
          </div>

          <Separator />

          <div className="space-y-2">
            <h3 className="font-medium">Components</h3>
            <ul className="space-y-2">
              {build.components.map((component) => (
                <li key={component.type} className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">{component.type}</p>
                    <p className="text-sm text-muted-foreground">{component.name}</p>
                  </div>
                  <p className="font-medium">${component.price.toFixed(2)}</p>
                </li>
              ))}
            </ul>
          </div>

          <Separator />

          <div className="space-y-2">
            <h3 className="font-medium">Buy Components</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {build.components.map((component) => (
                <Card key={component.type} className="overflow-hidden">
                  <CardHeader className="p-3 pb-0">
                    <CardTitle className="text-sm">{component.type}</CardTitle>
                    <CardDescription className="text-xs truncate">{component.name}</CardDescription>
                  </CardHeader>
                  <CardContent className="p-3 pt-2">
                    <div className="grid grid-cols-2 items-center gap-2">
                      {getRetailerLinks(component.name).map((retailer) => (
                        <Button
                          key={retailer.name}
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          asChild
                        >
                          <a href={retailer.url} target="_blank" rel="noopener noreferrer" className="flex items-center">
                            <Image src={retailer.icon} alt={retailer.name} width={16} height={16} />
                            {retailer.name}
                          </a>
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            className="flex-1"
            onClick={onSave}
          >
            <Save className="mr-2 h-4 w-4" />
            Save Build
          </Button>
          <Button
            variant="outline"
            className="flex-1"
            onClick={onShare}
          >
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button
            variant="outline"
            className="flex-1"
            onClick={onExport}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button className="w-full mt-2">
            <ShoppingCart className="mr-2 h-4 w-4" />
            Buy All Components
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}

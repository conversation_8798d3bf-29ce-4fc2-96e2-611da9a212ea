# DIYPC Implementation Strategy

**Version:** 1.0
**Date:** May 18, 2025

## 1. Tech Stack

- **Frontend Framework:** Next.js (App Router)
- **UI Components:** ShadCN/UI
- **Styling:** Tailwind CSS
- **Backend & Database:** Firebase (Firestore, Authentication, App Check)
- **AI Integration:** Vertex AI in Firebase SDK for web
- **Language Model (LLM):** Google Gemini 2.5 Flash
- **Version Control:** Git

## 2. Project Structure (Next.js App Router)

```
/diypc
├── /app                  # Next.js App Router directory
│   ├── (auth)            # Route group for authentication pages
│   │   ├── /sign-in
│   │   │   └── page.tsx
│   │   └── /sign-up
│   │       └── page.tsx
│   ├── (main)            # Route group for main application authenticated routes
│   │   ├── /dashboard      # User dashboard (saved builds, etc.)
│   │   │   └── page.tsx
│   │   ├── /build          # Main PC building interface
│   │   │   ├── /new        # Start a new build configuration
│   │   │   │   └── page.tsx
│   │   │   ├── /[buildId]  # View/edit an existing build
│   │   │   │   └── page.tsx
│   │   │   └── /recommendations # Display AI recommended builds
│   │   │       └── page.tsx
│   │   └── layout.tsx      # Layout for authenticated main sections
│   ├── /api                # API routes (if needed beyond Firebase SDK client-side calls)
│   │   └── /vertex-ai      # Potentially for server-side Vertex AI interactions if client-side proves insufficient
│   │       └── route.ts
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Landing page
│   └── globals.css         # Global styles
├── /components           # Shared UI components
│   ├── /auth             # Authentication related components (e.g., SignInForm.tsx)
│   ├── /build            # Components for the PC building interface (e.g., ComponentSelector.tsx, BuildSummary.tsx)
│   ├── /chat             # Components for the AI chat interface (e.g., ChatInput.tsx, ChatMessage.tsx)
│   └── /ui               # Generic UI components from ShadCN/UI (e.g., Button.tsx, Card.tsx)
├── /lib                  # Utility functions, Firebase configuration, Vertex AI SDK setup
│   ├── firebase.ts       # Firebase initialization and configuration
│   ├── vertex-ai.ts      # Vertex AI SDK initialization and helper functions
│   ├── utils.ts          # General utility functions
│   └── types.ts          # TypeScript type definitions
├── /public               # Static assets (images, fonts, etc.)
├── /hooks                # Custom React hooks (e.g., useAuth.ts, useChat.ts)
├── .env.local            # Environment variables (Firebase config, API keys)
├── .eslintrc.json
├── .gitignore
├── next.config.js
├── package.json
├── postcss.config.js
├── prd.md                # Product Requirements Document
├── README.md
├── tailwind.config.js
└── tsconfig.json
```

## 3. Business Logic & File Association

This section outlines the primary business logic and how it maps to the proposed file structure, based on the PRD.

### 3.1 Core AI Interaction & Build Process (`/app/(main)/build/new/page.tsx`, `/app/(main)/build/[buildId]/page.tsx`)

- **`app/(main)/build/new/page.tsx` & `app/(main)/build/[buildId]/page.tsx`:**
  - **Logic:** Manages the primary user flow for creating or modifying a PC build. This is where the AI interaction for requirements gathering and component recommendation occurs.
  - **Responsibilities:**
    - Initial user input collection (budget, primary use case) via forms.
    - Hosts the AI chat interface (`/components/chat/ChatInput.tsx`, `/components/chat/ChatMessage.tsx`).
    - Integrates with `lib/vertex-ai.ts` to send user queries to Gemini 2.5 Flash and receive responses.
    - Parses AI responses to extract clarifying questions, component recommendations, and explanations.
    - Dynamically updates the UI to display AI questions, recommended components (`/components/build/ComponentSelector.tsx`), and build summaries (`/components/build/BuildSummary.tsx`).
    - Handles state management for the current build configuration (potentially using `useState`, `useReducer`, or a state management library like Zustand/Jotai, or `nuqs` for URL-based state if shareability is prioritized for every step).
    - Implements compatibility checking logic, potentially by sending the current build to Gemini for validation or using client-side rules supplemented by AI for complex cases.
    - Displays retailer links for selected components.
    - If user accounts are implemented, handles saving/loading builds to/from Firebase Firestore via helper functions in `lib/firebase.ts`.

### 3.2 AI Agent Logic (`/lib/vertex-ai.ts`)

- **`lib/vertex-ai.ts`:**
  - **Logic:** Encapsulates all interactions with the Vertex AI in Firebase SDK and the Gemini 2.5 Flash model.
  - **Responsibilities:**
    - Initializes the Vertex AI SDK.
    - Provides functions to send prompts/queries to Gemini 2.5 Flash.
    - Differentiates between 'thinking' and 'non-thinking' modes for Gemini calls as outlined in PRD section 5.0.
    - Formats prompts for various tasks:
      - Natural language understanding for requirements gathering (PRD 4.1.1).
      - Generating clarifying questions (PRD 4.1.1).
      - Component recommendation based on requirements and knowledge base (PRD 4.1.2).
      - Compatibility validation (PRD 4.1.3).
      - Generating explanations for component choices (PRD 4.1.4).
      - Generating pre-configured build suggestions (PRD 4.1.5) - this might also involve a separate flow or admin interface.
    - Parses and structures responses from Gemini for easy consumption by the UI components.
    - Manages context for conversational AI interactions (e.g., chat history).
    - Handles API error responses and retries if necessary.

### 3.3 Component Data & Compatibility Rules (Managed by AI & Prompts)

- **Logic:** As per PRD section 5.0 and 5.1, the component specifications, compatibility rules, pricing, and benchmarks are primarily accessed and reasoned over by the Gemini 2.5 Flash model. The application will provide context and query the model effectively.
- **Responsibilities (within `lib/vertex-ai.ts` prompt engineering and interaction logic):**
  - Structuring prompts to Gemini to leverage its knowledge base regarding:
    - Component specifications (CPU, GPU, RAM, etc. - PRD 5.1).
    - Compatibility rules (socket matching, clearance, PSU wattage - PRD 5.1).
    - Pricing trends and availability (if the model has access to up-to-date information or via function calling if extended).
    - Performance benchmarks.
  - The application itself won't store a massive, static database of components if relying on the LLM's knowledge. However, it might cache frequently accessed information or popular component details to reduce LLM calls.

### 3.4 User Authentication & Data (`/lib/firebase.ts`, `/app/(auth)/*`, `/hooks/useAuth.ts`)

- **`lib/firebase.ts`:**
  - **Logic:** Initializes Firebase services (Auth, Firestore, App Check).
  - **Responsibilities:** Provides helper functions for:
    - User sign-up, sign-in, sign-out (Firebase Authentication).
    - Saving user builds to Firestore (if user accounts are implemented - PRD 4.1.8).
    - Retrieving user builds from Firestore.
    - Setting up Firebase App Check to protect backend resources and Vertex AI calls.
- **`app/(auth)/*`:** Contains pages for sign-in and sign-up, utilizing components from `/components/auth`.
- **`hooks/useAuth.ts`:** A custom hook to manage authentication state and provide user information throughout the app.

### 3.5 UI Components (`/components/*`)

- **`/components/chat/ChatInput.tsx`, `/components/chat/ChatMessage.tsx`:**
  - **Logic:** Handles the presentation and user interaction for the AI chat interface.
- **`/components/build/ComponentSelector.tsx`, `/components/build/BuildSummary.tsx`:**
  - **Logic:** Displays selected components, allows for manual changes (with AI validation), and shows the overall build summary with pricing.
- **`/components/ui/*`:** Re-exported or custom-styled ShadCN/UI components.

### 3.6 Pre-configured Builds (`/app/(main)/build/recommendations/page.tsx` or similar)

- **Logic:** Displays curated build templates (PRD 4.1.5).
- **Responsibilities:**
  - Fetches pre-configured build data. This data could be:
    - Statically defined.
    - Generated and stored in Firestore by an admin process using Gemini (via `lib/vertex-ai.ts`).
    - Dynamically generated by Gemini on request.
  - Allows users to select a template as a starting point for customization.

### 3.7 State Management

- **Client-Side State:** React's built-in state (`useState`, `useReducer`) and context (`useContext`) for local component state and simple global state.
- **URL State:** `nuqs` library for managing state that needs to be shareable via URL (e.g., current build parameters, chat session ID for shareable links as per US2.3).
- **Server-Side State (Data Persistence):** Firebase Firestore for user accounts and saved builds.

This structure and logic distribution aim to leverage Next.js App Router for organization, Firebase for backend services, and the Vertex AI SDK for direct, client-side interaction with Gemini 2.5 Flash, as emphasized in the PRD.

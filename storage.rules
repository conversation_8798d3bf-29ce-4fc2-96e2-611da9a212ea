rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if the user owns the profile image
    function isProfileOwner(userId) {
      return request.auth.uid == userId;
    }
    
    // Rules for profile images
    match /profile-images/{userId}/{imageId} {
      // Allow read if user is authenticated
      allow read: if isAuthenticated();
      
      // Allow write if user is authenticated and owns the profile
      allow write: if isAuthenticated() && isProfileOwner(userId);
      
      // Allow delete if user is authenticated and owns the profile
      allow delete: if isAuthenticated() && isProfileOwner(userId);
    }
    
    // Deny all other requests by default
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}

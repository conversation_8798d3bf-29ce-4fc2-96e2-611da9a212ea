/**
 * Types for affiliate links management
 */

import { ComponentType } from './pc-build';

/**
 * Marketplace type
 * Represents different e-commerce marketplaces
 */
export type MarketplaceType =
  // Global marketplaces
  | 'amazon'
  | 'newegg'
  | 'walmart'
  | 'bestbuy'
  // Regional marketplaces
  | 'microcenter'
  | 'bhphotovideo'
  | 'canadacomputers'
  | 'memoryexpress'
  | 'cyberpuerta'
  | 'scan'
  | 'overclockers'
  | 'ebuyer'
  | 'alternate'
  | 'mindfactory'
  | 'caseking'
  | 'ldlc'
  | 'materiel'
  | 'pccomponentes'
  | 'mediamarkt'
  | 'flipkart'
  | 'jd'
  | 'rakuten'
  | 'coupang'
  | 'lazada'
  | 'shopee'
  | 'mwave'
  | 'pccasegear'
  | 'pbtech'
  | 'mercadolibre'
  | 'kabum'
  | 'takealot'
  | 'other';

/**
 * Country code type
 * ISO 3166-1 alpha-2 country codes
 */
export type CountryCode =
  // North America
  | 'US' | 'CA' | 'MX'
  // Europe
  | 'GB' | 'DE' | 'FR' | 'ES' | 'IT' | 'NL' | 'SE' | 'NO' | 'DK' | 'FI' | 'PL'
  // Asia
  | 'IN' | 'CN' | 'JP' | 'KR' | 'SG' | 'MY' | 'ID' | 'TH' | 'PH' | 'AE' | 'SA'
  // Oceania
  | 'AU' | 'NZ'
  // South America
  | 'BR' | 'AR' | 'CL' | 'CO' | 'PE'
  // Africa
  | 'ZA' | 'NG' | 'EG' | 'KE';

/**
 * Affiliate link interface
 * Represents a single affiliate link for a specific marketplace
 */
export interface AffiliateLink {
  marketplace: MarketplaceType;
  url: string;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Country affiliate links interface
 * Represents all affiliate links for a specific country
 */
export interface CountryAffiliateLinks {
  countryCode: CountryCode;
  links: AffiliateLink[];
}

/**
 * Component affiliate links interface
 * Represents a component with its affiliate links by country
 */
export interface ComponentAffiliateLinks {
  id?: string;
  componentType: ComponentType;
  name: string;
  brand: string;
  model: string;
  countryLinks: CountryAffiliateLinks[];
  isProcessed: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

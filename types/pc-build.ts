/**
 * Types for PC components and builds
 */

export type ComponentType = 'CPU' | 'GPU' | 'RAM' | 'Motherboard' | 'SSD' | 'HDD' | 'PSU' | 'Case' | 'CPU Cooler';

export type UseCase = 'gaming' | 'content' | 'work';

export interface ComponentSpecs {
  name: string;
  value: string | number | boolean;
}

export interface PCComponent {
  type: ComponentType;
  name: string;
  brand: string;
  model: string;
  price: number;
  specs: ComponentSpecs[];
  compatibility?: Record<string, unknown>;
  performance?: Record<string, unknown>;
}

export interface Compatibility {
  isCompatible: boolean;
  issues?: string[];
}

export interface PCBuild {
  id?: string;
  name: string;
  description?: string;
  totalPrice: number;
  components: PCComponent[];
  useCase: UseCase;
  budget: string;
  requirements?: string;
  compatibility: Compatibility;
  createdAt?: Date;
  updatedAt?: Date;
  userId?: string;
}

export interface RecommendedBuilds {
  gaming: PCBuild[];
  content: PCBuild[];
  work: PCBuild[];
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system' | 'model' | 'function';
  content: string;
}

export interface RetailerLink {
  name: string;
  url: string;
  icon: string;
}

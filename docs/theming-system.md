# DIYPC Theming System Documentation

## Overview

The DIYPC application implements a comprehensive theming system that allows users to switch between light and dark modes. The system is built on top of Tailwind CSS and leverages CSS variables for consistent styling across the application.

## Theme Variables

The theme variables are defined in `app/globals.css` and follow a structured approach:

- Light mode variables are defined in the `:root` selector
- Dark mode variables are defined in the `.dark` class selector
- All components use these variables for styling to ensure consistency

## Theme Provider

The application uses a React context provider (`ThemeProvider`) to manage theme state and provide theme-switching functionality throughout the application.

### Features

- Automatic detection of system color scheme preference
- Manual theme selection (light, dark, system)
- Persistence of theme preference in localStorage
- Real-time theme switching without page reload

## Usage

### Using the Theme Provider

The `ThemeProvider` is included in the root layout (`app/layout.tsx`) and wraps the entire application:

```tsx
<ThemeProvider defaultTheme="system" storageKey="diypc-theme">
  {children}
</ThemeProvider>
```

### Accessing Theme in Components

Use the `useTheme` hook to access the current theme and theme-switching functionality:

```tsx
import { useTheme } from '@/hooks/useTheme';

function MyComponent() {
  const { theme, setTheme } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <button onClick={() => setTheme('light')}>Light</button>
      <button onClick={() => setTheme('dark')}>Dark</button>
      <button onClick={() => setTheme('system')}>System</button>
    </div>
  );
}
```

### Theme Toggle Component

A pre-built `ThemeToggle` component is available for easy theme switching:

```tsx
import { ThemeToggle } from '@/components/theme/theme-toggle';

function Header() {
  return (
    <header>
      <ThemeToggle />
    </header>
  );
}
```

## Styling Components for Theme Compatibility

When creating new components, use Tailwind's utility classes that reference the theme variables:

```tsx
// Good - uses theme variables
<div className="bg-background text-foreground">
  <p className="text-primary">This text respects the current theme</p>
</div>

// Avoid - hardcoded colors
<div className="bg-white text-black">
  <p className="text-blue-500">This text doesn't respect the theme</p>
</div>
```

## Accessibility Considerations

- The theming system ensures sufficient contrast ratios between text and background colors
- Interactive elements maintain clear focus states in both light and dark modes
- All color choices follow WCAG 2.1 AA standards for accessibility

## Extending the Theme

To add new theme variables:

1. Add the variable to both the `:root` and `.dark` selectors in `app/globals.css`
2. Use the variable in your components via Tailwind's arbitrary value syntax or custom utility classes

Example:
```css
:root {
  /* Existing variables */
  --new-color: 142 28% 60%;
}

.dark {
  /* Existing variables */
  --new-color: 142 28% 40%;
}
```

Then use in components:
```tsx
<div className="text-[hsl(var(--new-color))]">
  Custom themed text
</div>
```

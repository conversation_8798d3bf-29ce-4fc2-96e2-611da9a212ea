'use client';

import { MainLayout } from '@/components/layout/main-layout';
import { motion } from 'framer-motion';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Cpu, Shield, Zap, MessageSquare, Share2, DollarSign } from 'lucide-react';

export default function AboutPage() {
  const features = [
    {
      icon: Cpu,
      title: 'AI-Powered Recommendations',
      description: 'Our advanced AI analyzes thousands of components to find the perfect match for your needs.',
    },
    {
      icon: Shield,
      title: 'Compatibility Checking',
      description: 'Never worry about incompatible parts with our automatic compatibility verification.',
    },
    {
      icon: MessageSquare,
      title: 'Interactive Chat',
      description: 'Have a natural conversation with our AI assistant to refine your build requirements.',
    },
    {
      icon: Zap,
      title: 'Performance Insights',
      description: 'Get detailed performance predictions for your specific use cases and applications.',
    },
    {
      icon: Share2,
      title: 'Save & Share Builds',
      description: 'Save your PC builds to your account and share them with friends or the community.',
    },
    {
      icon: DollarSign,
      title: 'Budget Optimization',
      description: 'Our AI helps you get the best performance possible within your specified budget.',
    },
  ];

  const team = [
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      image: '/team/alex.jpg',
      bio: 'PC building enthusiast with 15+ years of experience in the tech industry.',
      initials: 'AJ',
    },
    {
      name: 'Sarah Chen',
      role: 'CTO',
      image: '/team/sarah.jpg',
      bio: 'AI expert with a background in computer engineering and machine learning.',
      initials: 'SC',
    },
    {
      name: 'Michael Rodriguez',
      role: 'Lead Developer',
      image: '/team/michael.jpg',
      bio: 'Full-stack developer passionate about creating intuitive user experiences.',
      initials: 'MR',
    },
    {
      name: 'Emma Wilson',
      role: 'Product Manager',
      image: '/team/emma.jpg',
      bio: 'Former hardware reviewer with deep knowledge of PC components and market trends.',
      initials: 'EW',
    },
  ];

  return (
    <MainLayout>
      <div className="container max-w-7xl px-4 mx-auto">
        {/* Hero Section */}
        <section className="py-20 md:py-28">
          <motion.div
            className="max-w-3xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">About DIYPC</h1>
            <p className="text-xl text-muted-foreground mb-8">
              We&apos;re on a mission to make custom PC building accessible to everyone through the power of AI.
            </p>
            <Separator className="max-w-md mx-auto" />
          </motion.div>
        </section>

        {/* Our Story Section */}
        <section className="py-16">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-3xl font-bold mb-6">Our Story</h2>
              <div className="space-y-4 text-lg">
                <p>
                  DIYPC was born from a simple observation: building a custom PC should be exciting, not intimidating.
                </p>
                <p>
                  Our founder, a lifelong PC enthusiast, noticed that many people were missing out on the benefits of custom PCs due to the complexity of component selection and compatibility concerns.
                </p>
                <p>
                  In 2024, we assembled a team of AI experts and PC building veterans to create a platform that would make custom PC building accessible to everyone, regardless of technical expertise.
                </p>
                <p>
                  Today, DIYPC helps thousands of users create their perfect custom computers with AI-powered recommendations, compatibility checking, and expert guidance.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="rounded-lg overflow-hidden bg-muted/50 aspect-video"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <div className="w-full h-full flex items-center justify-center">
                <p className="text-muted-foreground">Company image placeholder</p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-muted/30 rounded-lg px-6 my-16">
          <motion.div
            className="text-center max-w-3xl mx-auto mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl font-bold mb-4">What Makes Us Different</h2>
            <p className="text-lg text-muted-foreground">
              We combine cutting-edge AI technology with deep PC building expertise to create a unique experience.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, i) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: i * 0.1 }}
                >
                  <Card>
                    <CardContent className="pt-6">
                      <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                        <Icon className="h-6 w-6 text-primary" />
                      </div>
                      <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                      <p className="text-muted-foreground">{feature.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </section>

        {/* Team Section */}
        <section className="py-16">
          <motion.div
            className="text-center max-w-3xl mx-auto mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl font-bold mb-4">Meet Our Team</h2>
            <p className="text-lg text-muted-foreground">
              The passionate people behind DIYPC who are dedicated to revolutionizing PC building.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {team.map((member, i) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: i * 0.1 }}
                className="text-center"
              >
                <Avatar className="h-32 w-32 mx-auto mb-4">
                  <AvatarImage src={member.image} alt={member.name} />
                  <AvatarFallback className="text-2xl">{member.initials}</AvatarFallback>
                </Avatar>
                <h3 className="text-xl font-bold">{member.name}</h3>
                <p className="text-primary mb-2">{member.role}</p>
                <p className="text-muted-foreground">{member.bio}</p>
              </motion.div>
            ))}
          </div>
        </section>
      </div>
    </MainLayout>
  );
}

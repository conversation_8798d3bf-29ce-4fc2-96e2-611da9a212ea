'use client';

import { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { ComponentType } from '@/types/pc-build';
import { CountryCode, MarketplaceType, AffiliateLink } from '@/types/affiliate-links';
import { useAdminStore } from '@/lib/store/admin-store';
import { ArrowLeft, Plus, Trash, ExternalLink, Check, X, Loader2, Globe } from 'lucide-react';
import Link from 'next/link';
import { countryData, marketplaceData } from '@/lib/utils/country-marketplace-data';

interface ComponentEditPageProps {
  params: Promise<{
    componentType: string;
    componentId: string;
  }>;
}

export default function ComponentEditPage({ params }: ComponentEditPageProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>('details');
  const [newCountry, setNewCountry] = useState<CountryCode>('US');
  const [newMarketplace, setNewMarketplace] = useState<MarketplaceType>('amazon');
  const [newUrl, setNewUrl] = useState<string>('');
  const [selectedCountry, setSelectedCountry] = useState<CountryCode | null>(null);

  // State to store resolved params
  const [resolvedParams, setResolvedParams] = useState<{
    componentType: string;
    componentId: string;
  } | null>(null);

  const {
    selectedComponent,
    fetchComponentById,
    addCountryToComponent,
    removeCountryFromComponent,
    addAffiliateLinkToCountry,
    updateAffiliateLink,
    removeAffiliateLink,
    markComponentAsProcessed,
    isLoading,
    isSubmitting,
    error
  } = useAdminStore();

  // Resolve params on mount
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParamsData = await params;
      setResolvedParams(resolvedParamsData);
    };

    resolveParams();
  }, [params]);

  // Convert URL param to component type
  const getComponentType = (): ComponentType => {
    if (!resolvedParams) return 'CPU';

    const typeMap: Record<string, ComponentType> = {
      'cpu': 'CPU',
      'gpu': 'GPU',
      'ram': 'RAM',
      'motherboard': 'Motherboard',
      'ssd': 'SSD',
      'hdd': 'HDD',
      'psu': 'PSU',
      'case': 'Case',
      'cpu-cooler': 'CPU Cooler'
    };

    return typeMap[resolvedParams.componentType] || 'CPU';
  };

  const componentType = getComponentType();

  // Fetch component when params are resolved
  useEffect(() => {
    if (resolvedParams) {
      fetchComponentById(componentType, resolvedParams.componentId);
    }
  }, [fetchComponentById, componentType, resolvedParams]);

  // Set selected country to first country in list if available
  useEffect(() => {
    if (selectedComponent && selectedComponent.countryLinks.length > 0 && !selectedCountry) {
      setSelectedCountry(selectedComponent.countryLinks[0].countryCode);
    }
  }, [selectedComponent, selectedCountry]);

  // Handle adding a new country
  const handleAddCountry = async () => {
    if (!selectedComponent || !resolvedParams) return;

    try {
      await addCountryToComponent(resolvedParams.componentId, newCountry);
      setSelectedCountry(newCountry);
      toast({
        title: 'Country added',
        description: `${newCountry} has been added to the component.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    }
  };

  // Handle removing a country
  const handleRemoveCountry = async (country: CountryCode) => {
    if (!selectedComponent || !resolvedParams) return;

    try {
      await removeCountryFromComponent(resolvedParams.componentId, country);

      // If the removed country was selected, select another country
      if (selectedCountry === country) {
        const remainingCountries = selectedComponent.countryLinks
          .filter(c => c.countryCode !== country)
          .map(c => c.countryCode);

        setSelectedCountry(remainingCountries.length > 0 ? remainingCountries[0] : null);
      }

      toast({
        title: 'Country removed',
        description: `${country} has been removed from the component.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    }
  };

  // Handle adding a new affiliate link
  const handleAddAffiliateLink = async () => {
    if (!selectedComponent || !selectedCountry || !resolvedParams) return;

    if (!newUrl.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a valid URL',
        variant: 'destructive',
      });
      return;
    }

    try {
      const newLink: AffiliateLink = {
        marketplace: newMarketplace,
        url: newUrl,
        isActive: true,
      };

      await addAffiliateLinkToCountry(resolvedParams.componentId, selectedCountry, newLink);

      // Reset form
      setNewMarketplace('amazon');
      setNewUrl('');

      toast({
        title: 'Affiliate link added',
        description: `${newMarketplace} link has been added for ${selectedCountry}.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    }
  };

  // Handle updating an affiliate link
  const handleUpdateAffiliateLink = async (country: CountryCode, marketplace: MarketplaceType, isActive: boolean) => {
    if (!selectedComponent || !resolvedParams) return;

    try {
      await updateAffiliateLink(resolvedParams.componentId, country, marketplace, { isActive });

      toast({
        title: 'Affiliate link updated',
        description: `${marketplace} link for ${country} has been ${isActive ? 'activated' : 'deactivated'}.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    }
  };

  // Handle removing an affiliate link
  const handleRemoveAffiliateLink = async (country: CountryCode, marketplace: MarketplaceType) => {
    if (!selectedComponent || !resolvedParams) return;

    try {
      await removeAffiliateLink(resolvedParams.componentId, country, marketplace);

      toast({
        title: 'Affiliate link removed',
        description: `${marketplace} link has been removed for ${country}.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    }
  };

  // Handle marking component as processed
  const handleMarkAsProcessed = async () => {
    if (!selectedComponent || !resolvedParams) return;

    try {
      await markComponentAsProcessed(componentType, resolvedParams.componentId);

      toast({
        title: 'Component processed',
        description: `${selectedComponent.name} has been marked as processed.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    }
  };

  // Show loading state while params are being resolved
  if (!resolvedParams) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container max-w-7xl px-4 py-12 mx-auto">
        <div className="flex flex-col items-start gap-4 md:flex-row md:justify-between md:items-center mb-8">
          <div className="flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href={`/admin/${resolvedParams.componentType}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {selectedComponent ? selectedComponent.name : 'Loading...'}
              </h1>
              <p className="text-muted-foreground mt-1">
                {selectedComponent ? `${selectedComponent.brand} ${selectedComponent.model}` : ''}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {selectedComponent && !selectedComponent.isProcessed && (
              <Button onClick={handleMarkAsProcessed} disabled={isSubmitting}>
                {isSubmitting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Check className="h-4 w-4 mr-2" />
                )}
                Mark as Processed
              </Button>
            )}
          </div>
        </div>

        {error && (
          <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-8">
            <p>{error}</p>
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : selectedComponent ? (
          <Tabs defaultValue={activeTab} className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2 max-w-md mb-8">
              <TabsTrigger value="details">Component Details</TabsTrigger>
              <TabsTrigger value="links">Affiliate Links</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Component Information</CardTitle>
                  <CardDescription>Details about the component</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Name</Label>
                      <Input id="name" value={selectedComponent.name} disabled />
                    </div>
                    <div>
                      <Label htmlFor="brand">Brand</Label>
                      <Input id="brand" value={selectedComponent.brand} disabled />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="model">Model</Label>
                      <Input id="model" value={selectedComponent.model} disabled />
                    </div>
                    <div>
                      <Label htmlFor="type">Type</Label>
                      <Input id="type" value={selectedComponent.componentType} disabled />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <div className="flex items-center mt-2">
                      {selectedComponent.isProcessed ? (
                        <div className="flex items-center text-green-500">
                          <Check className="h-5 w-5 mr-2" />
                          <span>Processed</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-amber-500">
                          <X className="h-5 w-5 mr-2" />
                          <span>Not Processed</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="links" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="md:col-span-1">
                  <CardHeader>
                    <CardTitle>Countries</CardTitle>
                    <CardDescription>Add or select countries</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex flex-col space-y-2">
                        <Label htmlFor="country">Add Country</Label>
                        <div className="flex space-x-2">
                          <Select value={newCountry} onValueChange={(value) => setNewCountry(value as CountryCode)}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select country" />
                            </SelectTrigger>
                            <SelectContent>
                              {Object.keys(countryData).map((code) => (
                                <SelectItem key={code} value={code}>
                                  {countryData[code as CountryCode].name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button onClick={handleAddCountry} disabled={isSubmitting}>
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Available Countries</Label>
                        <div className="border rounded-md overflow-hidden">
                          {selectedComponent.countryLinks.length > 0 ? (
                            <div className="divide-y">
                              {selectedComponent.countryLinks.map((country) => (
                                <button
                                  key={country.countryCode}
                                  className={`flex items-center justify-between w-full p-3 text-left cursor-pointer hover:bg-muted/50 ${selectedCountry === country.countryCode ? 'bg-muted' : ''
                                    }`}
                                  onClick={() => setSelectedCountry(country.countryCode)}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                      e.preventDefault();
                                      setSelectedCountry(country.countryCode);
                                    }
                                  }}
                                  role="button"
                                  tabIndex={0}
                                  aria-pressed={selectedCountry === country.countryCode}
                                >
                                  <div className="flex items-center">
                                    <Globe className="h-4 w-4 mr-2 text-muted-foreground" />
                                    <span>{countryData[country.countryCode].name}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <span className="text-xs text-muted-foreground mr-2">
                                      {country.links.length} links
                                    </span>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleRemoveCountry(country.countryCode);
                                      }}
                                      disabled={isSubmitting}
                                    >
                                      <Trash className="h-4 w-4 text-destructive" />
                                    </Button>
                                  </div>
                                </button>
                              ))}
                            </div>
                          ) : (
                            <div className="p-4 text-center text-muted-foreground">
                              No countries added yet
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle>Affiliate Links</CardTitle>
                    <CardDescription>
                      {selectedCountry
                        ? `Manage affiliate links for ${countryData[selectedCountry].name}`
                        : 'Select a country to manage affiliate links'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {selectedCountry ? (
                      <div className="space-y-6">
                        <div className="space-y-4">
                          <Label>Add Affiliate Link</Label>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="md:col-span-1">
                              <Select
                                value={newMarketplace}
                                onValueChange={(value) => setNewMarketplace(value as MarketplaceType)}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select marketplace" />
                                </SelectTrigger>
                                <SelectContent>
                                  {Object.keys(marketplaceData).map((marketplace) => (
                                    <SelectItem key={marketplace} value={marketplace}>
                                      {marketplaceData[marketplace as MarketplaceType].name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="md:col-span-2 flex space-x-2">
                              <Input
                                placeholder="Affiliate URL"
                                value={newUrl}
                                onChange={(e) => setNewUrl(e.target.value)}
                              />
                              <Button onClick={handleAddAffiliateLink} disabled={isSubmitting}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add
                              </Button>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Existing Links</Label>
                          {selectedComponent.countryLinks.find(c => c.countryCode === selectedCountry)?.links.length ? (
                            <div className="rounded-md border">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead>Marketplace</TableHead>
                                    <TableHead>URL</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {selectedComponent.countryLinks
                                    .find(c => c.countryCode === selectedCountry)
                                    ?.links.map((link) => (
                                      <TableRow key={link.marketplace}>
                                        <TableCell className="font-medium">
                                          {marketplaceData[link.marketplace].name}
                                        </TableCell>
                                        <TableCell className="max-w-xs truncate">
                                          <a
                                            href={link.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="flex items-center text-blue-500 hover:underline"
                                          >
                                            <span className="truncate">{link.url}</span>
                                            <ExternalLink className="h-3 w-3 ml-1 flex-shrink-0" />
                                          </a>
                                        </TableCell>
                                        <TableCell>
                                          <div className="flex items-center space-x-2">
                                            <Switch
                                              checked={link.isActive}
                                              onCheckedChange={(checked) =>
                                                handleUpdateAffiliateLink(selectedCountry, link.marketplace, checked)
                                              }
                                              disabled={isSubmitting}
                                            />
                                            <span>{link.isActive ? 'Active' : 'Inactive'}</span>
                                          </div>
                                        </TableCell>
                                        <TableCell className="text-right">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleRemoveAffiliateLink(selectedCountry, link.marketplace)}
                                            disabled={isSubmitting}
                                          >
                                            <Trash className="h-4 w-4 mr-2 text-destructive" />
                                            Remove
                                          </Button>
                                        </TableCell>
                                      </TableRow>
                                    ))
                                  }
                                </TableBody>
                              </Table>
                            </div>
                          ) : (
                            <div className="p-4 text-center text-muted-foreground border rounded-md">
                              No affiliate links added for this country yet
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-12">
                        <Globe className="h-12 w-12 text-muted-foreground mb-4" />
                        <p className="text-muted-foreground text-center">
                          Select a country from the left panel to manage affiliate links
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 px-4 border rounded-md">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">Component not found</h3>
              <p className="text-muted-foreground mb-6">
                The component you are looking for does not exist or has been removed.
              </p>
              <Button asChild>
                <Link href={`/admin/${resolvedParams.componentType}`}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Components
                </Link>
              </Button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
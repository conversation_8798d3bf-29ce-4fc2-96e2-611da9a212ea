'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ComponentType, PCComponent } from '@/types/pc-build';
import { ComponentAffiliateLinks } from '@/types/affiliate-links';
import { useAdminStore } from '@/lib/store/admin-store';
import { Plus, Edit, Check, X, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { DataTable } from '@/components/admin/data-table';
import { ColumnDef } from '@tanstack/react-table';

interface ComponentTypePageProps {
  params: Promise<{
    componentType: string;
  }>;
}

export default function ComponentTypePage({ params }: ComponentTypePageProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<string>('processed');
  const {
    componentsByType,
    unprocessedComponents,
    fetchComponentsByType,
    fetchUnprocessedComponents,
    addComponent,
    isLoading,
    isSubmitting,
    error
  } = useAdminStore();

  // Resolve params on mount
  const [resolvedParams, setResolvedParams] = useState<{
    componentType: string;
  } | null>(null);

  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParamsData = await params;
      setResolvedParams(resolvedParamsData);
    };

    resolveParams();
  }, [params]);

  // Define columns for processed components
  const processedColumns = useMemo<ColumnDef<ComponentAffiliateLinks>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'brand',
      header: 'Brand',
    },
    {
      accessorKey: 'model',
      header: 'Model',
    },
    {
      accessorKey: 'countryLinks',
      header: 'Countries',
      cell: ({ row }) => row.original.countryLinks.length,
    },
    {
      accessorKey: 'links',
      header: 'Links',
      cell: ({ row }) => {
        return row.original.countryLinks.reduce((total, country) => total + country.links.length, 0);
      },
    },
    {
      accessorKey: 'isProcessed',
      header: 'Status',
      cell: ({ row }) => {
        return row.original.isProcessed ? (
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-1" />
            <span>Processed</span>
          </div>
        ) : (
          <div className="flex items-center">
            <X className="h-4 w-4 text-red-500 mr-1" />
            <span>Incomplete</span>
          </div>
        );
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        return (
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/${resolvedParams?.componentType}/${row.original.id}`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        );
      },
    },
  ], [resolvedParams?.componentType]);

  // Add unprocessed component to affiliate links collection
  const handleAddComponent = useMemo(() => async (component: PCComponent) => {
    try {
      const newComponent: ComponentAffiliateLinks = {
        componentType: component.type,
        name: component.name,
        brand: component.brand,
        model: component.model,
        countryLinks: [],
        isProcessed: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const componentId = await addComponent(newComponent);
      router.push(`/admin/${resolvedParams?.componentType}/${componentId}`);
    } catch (error) {
      console.error('Error adding component:', error);
    }
  }, [addComponent, router, resolvedParams?.componentType]);

  // Define columns for unprocessed components
  const unprocessedColumns = useMemo<ColumnDef<PCComponent>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'brand',
      header: 'Brand',
    },
    {
      accessorKey: 'model',
      header: 'Model',
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleAddComponent(row.original)}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Plus className="h-4 w-4 mr-2" />
            )}
            Add
          </Button>
        );
      },
    },
  ], [handleAddComponent, isSubmitting]);

  // Convert URL param to component type
  const getComponentType = (): ComponentType => {
    if (!resolvedParams) return 'CPU';

    const typeMap: Record<string, ComponentType> = {
      'cpu': 'CPU',
      'gpu': 'GPU',
      'ram': 'RAM',
      'motherboard': 'Motherboard',
      'ssd': 'SSD',
      'hdd': 'HDD',
      'psu': 'PSU',
      'case': 'Case',
      'cpu-cooler': 'CPU Cooler'
    };

    return typeMap[resolvedParams.componentType] || 'CPU';
  };

  const componentType = getComponentType();

  // Fetch components on mount
  useEffect(() => {
    fetchComponentsByType(componentType);
    fetchUnprocessedComponents(componentType);
  }, [fetchComponentsByType, fetchUnprocessedComponents, componentType]);

  // No need to filter components as DataTable handles filtering

  return (
    <AdminLayout>
      <div className="container max-w-7xl px-4 py-12 mx-auto">
        <div className="flex flex-col items-start gap-4 md:flex-row md:justify-between md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{componentType} Components</h1>
            <p className="text-muted-foreground mt-1">Manage affiliate links for {componentType} components</p>
          </div>
        </div>

        {error && (
          <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-8">
            <p>{error}</p>
          </div>
        )}

        <Tabs defaultValue={activeTab} className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 max-w-md mb-8">
            <TabsTrigger value="processed">Processed</TabsTrigger>
            <TabsTrigger value="unprocessed">Unprocessed</TabsTrigger>
          </TabsList>

          <TabsContent value="processed" className="mt-0">
            {(() => {
              if (isLoading) {
                return (
                  <div className="flex justify-center items-center py-20">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                );
              }

              if (componentsByType[componentType]?.length > 0) {
                return (
                  <DataTable
                    columns={processedColumns}
                    data={componentsByType[componentType] || []}
                    searchColumn="name"
                    searchPlaceholder={`Search ${componentType} components...`}
                  />
                );
              }

              return (
                <div className="flex flex-col items-center justify-center py-12 px-4 border rounded-md">
                  <div className="text-center">
                    <h3 className="text-lg font-medium mb-2">No processed components</h3>
                    <p className="text-muted-foreground mb-6">
                      There are no processed {componentType} components yet.
                    </p>
                  </div>
                </div>
              );
            })()}
          </TabsContent>

          <TabsContent value="unprocessed" className="mt-0">
            {(() => {
              if (isLoading) {
                return (
                  <div className="flex justify-center items-center py-20">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                );
              }

              if (unprocessedComponents[componentType]?.length > 0) {
                return (
                  <DataTable
                    columns={unprocessedColumns}
                    data={unprocessedComponents[componentType] || []}
                    searchColumn="name"
                    searchPlaceholder={`Search ${componentType} components...`}
                  />
                );
              }

              return (
                <div className="flex flex-col items-center justify-center py-12 px-4 border rounded-md">
                  <div className="text-center">
                    <h3 className="text-lg font-medium mb-2">No unprocessed components</h3>
                    <p className="text-muted-foreground mb-6">
                      There are no unprocessed {componentType} components to add.
                    </p>
                  </div>
                </div>
              );
            })()}
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, MessageSquare, Settings, Loader2, Save } from 'lucide-react';
import { ChatMessages } from '@/components/chat/chat-message';
import { ChatInterface } from '@/components/chat/chat-input';
import { ComponentSelector } from '@/components/build/component-selector';
import { BuildSummary } from '@/components/build/build-summary';
import { PCComponent } from '@/types/pc-build';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useBuildStore } from '@/lib/store/build-store';
import { useAuthStore } from '@/lib/store/auth-store';
import ReactMarkdown from 'react-markdown';

export default function NewBuildPage() {
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  const totalSteps = 2; // Reduced to 2 steps: Chat+Preferences and Component Selection

  // State variables
  const [buildName, setBuildName] = useState<string>('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<PCComponent['type'] | null>(null);
  const [componentExplanations, setComponentExplanations] = useState<Record<string, string>>({});

  // Chat flow control
  // Use the build store
  const currentBuild = useBuildStore((state) => state.currentBuild);
  const chatMessages = useBuildStore((state) => state.chatMessages);
  const sendChatMessage = useBuildStore((state) => state.sendChatMessage);
  const initializeChat = useBuildStore((state) => state.initializeChat);
  const isLoadingChatResponse = useBuildStore((state) => state.isLoadingChatResponse);
  const saveBuild = useBuildStore((state) => state.saveBuild);

  // Use the auth store
  const user = useAuthStore((state) => state.user);

  // Local loading state
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  // Build generation is now handled directly by the chat API

  // Removed formatUserPreferences function

  const handleSendMessage = async (message: string) => {
    if (isLoading) return;

    try {
      // Send the message to the chat API
      // The build-store will automatically extract and set the build if one is generated
      await sendChatMessage(message);

      // If a build was generated, the currentBuild state will be updated automatically
      if (currentBuild && !buildName) {
        // Set a default name for the build based on its properties
        const useCaseText = currentBuild.useCase.charAt(0).toUpperCase() + currentBuild.useCase.slice(1);
        setBuildName(`${useCaseText} Build (${currentBuild.budget})`);
      }
    } catch (error) {
      console.error('Error in chat:', error);
      toast({
        title: 'Error',
        description: 'Failed to get a response. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const handleViewExplanation = async (componentType: PCComponent['type']) => {
    setSelectedComponent(componentType);

    // Check if we already have an explanation for this component
    if (!componentExplanations[componentType] && currentBuild) {
      try {
        setIsLoading(true);

        // Find the component
        const component = currentBuild.components.find((c: PCComponent) => c.type === componentType);
        if (!component) return;

        // Generate explanation prompt for the component type
        const prompt = `
          Please explain why a ${component.name} is a good choice for a ${currentBuild.useCase} PC build with a budget of ${currentBuild.budget}.
          Focus on how it benefits this specific use case and its value for money.
        `;

        // Use the store's sendChatMessage function to get an explanation
        const response = await sendChatMessage(prompt);

        // Use the response or a generic explanation
        const explanation = response ?? `This ${component.name} is an excellent choice for your ${currentBuild.useCase} build within a ${currentBuild.budget} budget. It offers a great balance of performance and value.`;

        // Save the explanation
        setComponentExplanations((prev: Record<string, string>) => ({
          ...prev,
          [componentType]: explanation
        }));

      } catch (error) {
        console.error('Error generating explanation:', error);
        toast({
          title: 'Error',
          description: 'Failed to generate component explanation.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleSaveBuild = async () => {
    if (!currentBuild) return;

    try {
      setIsSaving(true);

      // Generate a temporary user ID if not authenticated
      const userId = user?.uid ?? ('guest-' + Math.random().toString(36).substring(2, 15));

      // Use the build's use case for the name
      const useCaseText = currentBuild.useCase.charAt(0).toUpperCase() + currentBuild.useCase.slice(1);

      // Save build to Firebase using the store's saveBuild function
      await saveBuild(userId, {
        ...currentBuild,
        name: buildName || `${useCaseText} Build (${currentBuild.budget})`
      });

      toast({
        title: 'Success',
        description: 'Your build has been saved successfully.',
      });

      setShowSaveDialog(false);

    } catch (error) {
      console.error('Error saving build:', error);
      toast({
        title: 'Error',
        description: 'Failed to save build. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleShareBuild = () => {
    // Implementation for sharing build
    toast({
      title: 'Share',
      description: 'Sharing functionality will be implemented soon.',
    });
  };

  const handleExportBuild = () => {
    // Implementation for exporting build
    if (!currentBuild) return;

    const buildData = JSON.stringify(currentBuild, null, 2);
    const blob = new Blob([buildData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${buildName || 'pc-build'}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: 'Success',
      description: 'Build exported successfully.',
    });
  };

  // Initialize chat with welcome message
  useEffect(() => {
    // Only initialize if we only have the system message
    if (chatMessages.length === 1 && chatMessages[0].role === 'system') {
      // Initialize the chat session
      initializeChat();
    }

    // Set a default build name if we have a build but no name
    if (currentBuild && !buildName) {
      const useCaseText = currentBuild.useCase.charAt(0).toUpperCase() + currentBuild.useCase.slice(1);
      setBuildName(`${useCaseText} Build (${currentBuild.budget})`);
    }
  }, [chatMessages, initializeChat, currentBuild, buildName]);

  return (
    <MainLayout>
      <div className="container max-w-7xl px-4 py-12 mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">Create a New Build</h1>
          <p className="text-muted-foreground mt-1">Let&apos;s build your perfect PC together</p>
        </div>

        {/* Progress bar */}
        <div className="w-full bg-muted h-2 rounded-full mb-8 overflow-hidden">
          <motion.div
            className="h-full bg-primary"
            initial={{ width: `${(1 / totalSteps) * 100}%` }}
            animate={{ width: `${(step / totalSteps) * 100}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Combined Chat and Preferences */}
          {step === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="grid gap-6">
                {/* Main Chat Area - Now full width */}
                <div>
                  <Card className="h-[65vh] flex flex-col">
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <MessageSquare className="mr-2 h-5 w-5" />
                        Chat with our AI Assistant
                      </CardTitle>
                      <CardDescription>Tell us what kind of PC you want to build, including your budget and use cases. Our AI will ask questions and generate a complete build for you.</CardDescription>
                    </CardHeader>
                    <CardContent className="flex-grow overflow-y-auto flex flex-col">
                      <div className="flex-grow overflow-y-auto mb-4 pr-2">
                        <ChatMessages
                          messages={chatMessages.filter((msg: { role: string, content: string }) =>
                            msg.role !== 'system' && !msg.content.startsWith('__INIT__')
                          )}
                          isLoading={isLoadingChatResponse}
                        />
                      </div>
                      <ChatInterface
                        messages={chatMessages.filter((msg: { role: string, content: string }) =>
                          msg.role !== 'system' && !msg.content.startsWith('__INIT__')
                        )}
                        onSendMessage={handleSendMessage}
                        isLoading={isLoadingChatResponse ?? isLoading}
                      />
                    </CardContent>
                    <CardFooter className="flex justify-end">
                      {currentBuild && (
                        <Button onClick={nextStep}>
                          Continue to Component Selection
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                </div>
              </div>
            </motion.div>
          )}

          {/* Step 2: Component Selection */}
          {step === 2 && currentBuild && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="grid gap-6 lg:grid-cols-5">
                <div className="lg:col-span-3">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Settings className="mr-2 h-5 w-5" />
                        Component Selection
                      </CardTitle>
                      <CardDescription>Review and customize your build</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ComponentSelector
                        build={currentBuild}
                        onViewExplanation={handleViewExplanation}
                      />
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button variant="outline" onClick={prevStep}>
                        Back to Chat
                      </Button>
                      <Button onClick={() => setShowSaveDialog(true)}>
                        <Save className="mr-2 h-4 w-4" />
                        Save Build
                      </Button>
                    </CardFooter>
                  </Card>
                </div>

                <div className="lg:col-span-2">
                  <BuildSummary
                    build={currentBuild}
                    onSave={() => setShowSaveDialog(true)}
                    onShare={handleShareBuild}
                    onExport={handleExportBuild}
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Component Explanation Dialog */}
        <Dialog open={!!selectedComponent} onOpenChange={() => setSelectedComponent(null)}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {selectedComponent && `About the ${selectedComponent}`}
              </DialogTitle>
              <DialogDescription>
                Why this component was chosen for your build
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4 max-h-[60vh] overflow-y-auto">
              {selectedComponent && componentExplanations[selectedComponent] ? (
                <div className="prose prose-sm dark:prose-invert">
                  <ReactMarkdown>{componentExplanations[selectedComponent]}</ReactMarkdown>
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Save Build Dialog */}
        <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Save Your Build</DialogTitle>
              <DialogDescription>
                Give your build a name to save it to your account
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4 space-y-4">
              <div className="space-y-2">
                <label htmlFor="build-name" className="text-sm font-medium">
                  Build Name
                </label>
                <Input
                  id="build-name"
                  value={buildName}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setBuildName(e.target.value)}
                  placeholder="My Awesome Gaming PC"
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveBuild} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Build
                  </>
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
}

import { MainLayout } from '@/components/layout/main-layout';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { ArrowRight, Plus, Zap } from 'lucide-react';

export default function BuildPage() {
  return (
    <MainLayout>
      <div className="container max-w-7xl px-4 py-12 mx-auto">
        <div className="flex flex-col items-start gap-4 md:flex-row md:justify-between md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">PC Builder</h1>
            <p className="text-muted-foreground mt-1">Create your custom PC with AI assistance</p>
          </div>
          <Button asChild>
            <Link href="/build/new">
              <Plus className="mr-2 h-4 w-4" />
              New Build
            </Link>
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="overflow-hidden">
            <CardHeader className="p-6">
              <CardTitle>Start a New Build</CardTitle>
              <CardDescription>Create a custom PC from scratch with AI guidance</CardDescription>
            </CardHeader>
            <CardContent className="p-6 pt-0">
              <p>Our AI assistant will help you select compatible components based on your needs and budget.</p>
            </CardContent>
            <CardFooter className="p-6 pt-0 flex justify-end">
              <Button asChild>
                <Link href="/build/new">
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card className="overflow-hidden">
            <CardHeader className="p-6">
              <CardTitle>AI Recommendations</CardTitle>
              <CardDescription>Browse pre-configured builds for different use cases</CardDescription>
            </CardHeader>
            <CardContent className="p-6 pt-0">
              <p>Explore AI-recommended builds optimized for gaming, content creation, work, and more.</p>
            </CardContent>
            <CardFooter className="p-6 pt-0 flex justify-end">
              <Button asChild variant="outline">
                <Link href="/build/recommendations">
                  View Recommendations
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card className="overflow-hidden">
            <CardHeader className="p-6">
              <CardTitle>Quick Build</CardTitle>
              <CardDescription>Get a build recommendation in seconds</CardDescription>
            </CardHeader>
            <CardContent className="p-6 pt-0">
              <p>Answer a few quick questions and get an instant PC build recommendation.</p>
            </CardContent>
            <CardFooter className="p-6 pt-0 flex justify-end">
              <Button asChild variant="secondary">
                <Link href="/build/quick">
                  <Zap className="mr-2 h-4 w-4" />
                  Quick Build
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}

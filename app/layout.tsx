import type { Metada<PERSON> } from "next";
import { Poppins, Source_Code_Pro } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import StoreProvider from "@/components/providers/store-provider";

const poppinsSans = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

const sourceCodePro = Source_Code_Pro({
  variable: "--font-source-code-pro",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "DIYPC - Build Your Perfect PC",
  description: "AI-powered PC building assistant to help you create your perfect custom PC",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="apple-mobile-web-app-title" content="DIYPC" />
        <link rel="apple-touch-icon" href="/apple-icon.png" type="image/png" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
      </head>
      <body
        className={`${poppinsSans.variable} ${sourceCodePro.variable} antialiased`}
      >
        <ThemeProvider defaultTheme="system" storageKey="diypc-theme" enableSystem attribute="class" enableColorScheme>
          <StoreProvider>
            {children}
            <Toaster />
          </StoreProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}

'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Particles } from '@/components/ui/particles';
import { useAuthStore, UserProfile } from '@/lib/store/auth-store';
import { UserService } from '@/lib/services/user-service';
import { StorageService } from '@/lib/services/storage-service';
import GoogleIcon from '@/public/google.svg';
import { Mail } from 'lucide-react';
import Image from 'next/image';

// Form schema
const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

type FormValues = z.infer<typeof formSchema>;

function SignInPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const { user, sendSignInLink, signInWithGoogle, isSignInWithEmailLink, signInWithEmailLink } = useAuthStore();

  // Get the redirect URL from query params
  const redirectUrl = searchParams.get('redirect') ?? '/dashboard';

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  // Check if the current URL is a sign-in link
  useEffect(() => {
    const checkEmailLink = async () => {
      if (isSignInWithEmailLink(window.location.href)) {
        // Get the email from localStorage
        let email = localStorage.getItem('emailForSignIn');

        if (!email) {
          // If email is not found in localStorage, prompt the user
          email = window.prompt('Please provide your email for confirmation');

          if (!email) {
            toast({
              title: 'Sign in failed',
              description: 'Email is required to complete sign in.',
              variant: 'destructive',
            });
            return;
          }
        }

        try {
          setIsLoading(true);
          const user = await signInWithEmailLink(email, window.location.href);

          // Get user profile data from localStorage if it exists (for new users)
          const userProfileJson = localStorage.getItem('userProfile');

          if (userProfileJson) {
            try {
              const userProfileData = JSON.parse(userProfileJson) as UserProfile;

              // Upload profile image if it exists
              let photoURL = userProfileData.photoURL;
              if (photoURL?.startsWith('data:')) {
                // Upload the image to Firebase Storage
                photoURL = await StorageService.uploadProfileImage(user.uid, photoURL);
              }

              // Create user profile in Firestore
              await UserService.createOrUpdateProfile(user.uid, {
                ...userProfileData,
                photoURL,
              });

              // Update user profile in Firebase Auth
              if (userProfileData.firstName && userProfileData.lastName) {
                await useAuthStore.getState().updateUserProfile({
                  firstName: userProfileData.firstName,
                  lastName: userProfileData.lastName,
                  photoURL,
                  country: userProfileData.country,
                  email: userProfileData.email,
                });
              }

              // Clear user profile from localStorage
              localStorage.removeItem('userProfile');
            } catch (profileError) {
              console.error('Error creating user profile:', profileError);
            }
          }

          // Clear email from storage
          localStorage.removeItem('emailForSignIn');

          toast({
            title: 'Sign in successful',
            description: 'You have been signed in successfully.',
          });

          // Redirect to dashboard or the specified redirect URL
          router.push(redirectUrl);
        } catch (error) {
          console.error('Error signing in with email link:', error);
          toast({
            title: 'Sign in failed',
            description: error instanceof Error ? error.message : 'An unknown error occurred',
            variant: 'destructive',
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    checkEmailLink();
  }, [isSignInWithEmailLink, signInWithEmailLink, router, toast, redirectUrl]);

  // Redirect if user is already signed in
  useEffect(() => {
    if (user) {
      router.push(redirectUrl);
    }
  }, [user, router, redirectUrl]);

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsLoading(true);

      // Configure action code settings
      const actionCodeSettings = {
        // URL you want to redirect back to. The domain (www.example.com) for this
        // URL must be in the authorized domains list in the Firebase Console.
        url: window.location.href,
        // This must be true.
        handleCodeInApp: true,
      };

      // Send sign-in link to email
      await sendSignInLink(values.email, actionCodeSettings);

      // Save the email locally so you don't need to ask the user for it again
      localStorage.setItem('emailForSignIn', values.email);

      setEmailSent(true);

      toast({
        title: 'Email sent',
        description: 'Check your email for a sign-in link.',
      });
    } catch (error) {
      console.error('Error sending sign-in link:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      const user = await signInWithGoogle();

      // Create user profile in Firestore if it doesn't exist
      await UserService.createProfileFromAuthUser(user);

      router.push(redirectUrl);
    } catch (error) {
      console.error('Error signing in with Google:', error);
      toast({
        title: 'Sign in failed',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="relative min-h-screen flex items-center justify-center py-12">
        {/* Particles background */}
        <div className="absolute inset-0">
          <Particles
            className="absolute inset-0"
            quantity={100}
            staticity={30}
            color="oklch(var(--primary))"
            ease={50}
            size={0.8}
          />
        </div>

        <div className="relative z-10 w-full max-w-md px-4 sm:px-0">
          <Card className="border shadow-lg">
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">Sign in</CardTitle>
              <CardDescription className="text-center">
                Sign in to your account to access your dashboard
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Google Sign In */}
              <Button
                variant="outline"
                className="w-full"
                onClick={handleGoogleSignIn}
                disabled={isLoading}
              >
                <Image src={GoogleIcon} alt="Google Icon" width={20} height={20} className="mr-2 h-4 w-4" />
                Sign in with Google
              </Button>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <Separator />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">
                    Or continue with
                  </span>
                </div>
              </div>

              {emailSent ? (
                <div className="text-center space-y-4">
                  <p className="text-sm text-muted-foreground">
                    We&apos;ve sent a sign-in link to your email. Please check your inbox and click the link to sign in.
                  </p>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setEmailSent(false)}
                  >
                    Use a different email
                  </Button>
                </div>
              ) : (
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isLoading}
                    >
                      <Mail className="mr-2 h-4 w-4" />
                      Sign in with Email
                    </Button>
                  </form>
                </Form>
              )}
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <div className="text-center text-sm text-muted-foreground">
                Don&apos;t have an account?{' '}
                <Link href="/auth/signup" className="underline text-primary hover:text-primary/90">
                  Sign up
                </Link>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}

function SignInWithSuspense() {
  return (
    <Suspense>
      <SignInPage />
    </Suspense>
  );
}

export default SignInWithSuspense;

@import 'tailwindcss';
@import 'tw-animate-css';

/* @custom-variant dark (&:is(.dark *)); */

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  --font-mono: var(--font-source-code-pro);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-accent-foreground: var(--accent-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-card-foreground: var(--card-foreground);
}




:root {
  --background: oklch(0.985 0 0);
  --card: oklch(0.985 0 0);
  --popover: oklch(0.985 0 0);
  --sidebar: oklch(0.985 0 0);
  --foreground: oklch(0.218 0 0);
  --card-foreground: oklch(0.218 0 0);
  --popover-foreground: oklch(0.218 0 0);
  --sidebar-foreground: oklch(0.218 0 0);
  --primary: oklch(0.489 0.071 155);
  --sidebar-primary: oklch(0.489 0.071 155);
  --primary-foreground: oklch(0.961 0 0);
  --sidebar-primary-foreground: oklch(0.961 0 0);
  --secondary: oklch(0.885 0 0);
  --accent: oklch(0.885 0 0);
  --sidebar-accent: oklch(0.885 0 0);
  --secondary-foreground: oklch(0.218 0 0);
  --accent-foreground: oklch(0.218 0 0);
  --sidebar-accent-foreground: oklch(0.218 0 0);
  --muted: oklch(0.901 0 0);
  --muted-foreground: oklch(0.6 0 0);
  --destructive: oklch(0.599 0.236 28);
  --destructive-foreground: oklch(0.961 0 0);
  --success: oklch(0.65 0.15 145);
  --success-foreground: oklch(0.961 0 0);
  --info: oklch(0.65 0.15 230);
  --info-foreground: oklch(0.961 0 0);
  --warning: oklch(0.75 0.15 80);
  --warning-foreground: oklch(0.961 0 0);
  --border: oklch(0.855 0 0);
  --sidebar-border: oklch(0.855 0 0);
  --ring: oklch(0.825 0.026 157);
  --sidebar-ring: oklch(0.825 0.026 157);
  --input: oklch(0.855 0 0);
  --chart-1: oklch(0.489 0.071 155);
  --chart-2: oklch(0.454 0.066 152);
  --chart-3: oklch(0.454 0.054 163);
  --chart-4: oklch(0.448 0.068 147);
  --chart-5: oklch(0.448 0.055 156);
  --radius: 0.5rem;
}

.dark {
  --background: oklch(0.218 0 0);
  --card: oklch(0.218 0 0);
  --popover: oklch(0.218 0 0);
  --sidebar: oklch(0.218 0 0);
  --foreground: oklch(0.961 0 0);
  --card-foreground: oklch(0.961 0 0);
  --popover-foreground: oklch(0.961 0 0);
  --sidebar-foreground: oklch(0.961 0 0);
  --primary: oklch(0.627 0.094 155);
  --sidebar-primary: oklch(0.627 0.094 155);
  --primary-foreground: oklch(0.961 0 0);
  --sidebar-primary-foreground: oklch(0.961 0 0);
  --secondary: oklch(0.301 0 0);
  --accent: oklch(0.301 0 0);
  --sidebar-accent: oklch(0.301 0 0);
  --secondary-foreground: oklch(0.961 0 0);
  --accent-foreground: oklch(0.961 0 0);
  --sidebar-accent-foreground: oklch(0.961 0 0);
  --muted: oklch(0.352 0 0);
  --muted-foreground: oklch(0.683 0 0);
  --destructive: oklch(0.608 0.23 28);
  --destructive-foreground: oklch(0.961 0 0);
  --success: oklch(0.7 0.17 145);
  --success-foreground: oklch(0.961 0 0);
  --info: oklch(0.7 0.17 230);
  --info-foreground: oklch(0.961 0 0);
  --warning: oklch(0.8 0.17 80);
  --warning-foreground: oklch(0.961 0 0);
  --border: oklch(0.333 0 0);
  --sidebar-border: oklch(0.333 0 0);
  --ring: oklch(0.404 0.044 156);
  --sidebar-ring: oklch(0.404 0.044 156);
  --input: oklch(0.333 0 0);
  --chart-1: oklch(0.627 0.094 155);
  --chart-2: oklch(0.653 0.102 152);
  --chart-3: oklch(0.675 0.078 163);
  --chart-4: oklch(0.687 0.09 147);
  --chart-5: oklch(0.708 0.067 157);
}

/* Add Tailwind v4 @theme configuration as needed */
/* Example: @theme { --color-primary: var(--primary); } */

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

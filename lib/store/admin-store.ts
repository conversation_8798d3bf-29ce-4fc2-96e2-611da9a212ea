'use client';

import { create } from 'zustand';
import { ComponentAffiliateLinks, AffiliateLink, CountryCode, MarketplaceType } from '@/types/affiliate-links';
import { ComponentType, PCBuild, PCComponent } from '@/types/pc-build';
import { FirestoreService } from '@/lib/services/firestore-service';

interface AdminState {
  // Component lists
  componentsByType: Record<ComponentType, ComponentAffiliateLinks[]>;
  unprocessedComponents: Record<ComponentType, PCComponent[]>;

  // Selected component
  selectedComponent: ComponentAffiliateLinks | null;

  // Loading states
  isLoading: boolean;
  isSubmitting: boolean;

  // Error handling
  error: string | null;

  // Actions
  fetchComponentsByType: (componentType: ComponentType) => Promise<void>;
  fetchUnprocessedComponents: (componentType: ComponentType) => Promise<void>;
  fetchComponentById: (componentType: ComponentType, componentId: string) => Promise<void>;
  addComponent: (component: ComponentAffiliateLinks) => Promise<string>;
  updateComponent: (componentId: string, component: Partial<ComponentAffiliateLinks>) => Promise<void>;
  addCountryToComponent: (componentId: string, country: CountryCode) => Promise<void>;
  removeCountryFromComponent: (componentId: string, country: CountryCode) => Promise<void>;
  addAffiliateLinkToCountry: (componentId: string, country: CountryCode, link: AffiliateLink) => Promise<void>;
  updateAffiliateLink: (componentId: string, country: CountryCode, marketplace: MarketplaceType, link: Partial<AffiliateLink>) => Promise<void>;
  removeAffiliateLink: (componentId: string, country: CountryCode, marketplace: MarketplaceType) => Promise<void>;
  clearSelectedComponent: () => void;
  markComponentAsProcessed: (componentType: ComponentType, componentId: string) => Promise<void>;
}

export const useAdminStore = create<AdminState>((set, get) => ({
  // Initial state
  componentsByType: {} as Record<ComponentType, ComponentAffiliateLinks[]>,
  unprocessedComponents: {} as Record<ComponentType, PCComponent[]>,
  selectedComponent: null,
  isLoading: false,
  isSubmitting: false,
  error: null,

  // Actions
  fetchComponentsByType: async (componentType: ComponentType) => {
    try {
      set({ isLoading: true, error: null });

      const collectionName = `affiliate-${componentType.toLowerCase().replace(' ', '-')}`;
      const components = await FirestoreService.queryDocuments(collectionName, []) as ComponentAffiliateLinks[];

      set(state => ({
        componentsByType: {
          ...state.componentsByType,
          [componentType]: components
        },
        isLoading: false
      }));
    } catch (error) {
      console.error(`Error fetching ${componentType} components:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  fetchUnprocessedComponents: async (componentType: ComponentType) => {
    try {
      set({ isLoading: true, error: null });

      // Get all builds from Firestore
      const builds = await FirestoreService.queryDocuments('builds', []) as PCBuild[];

      // Extract components of the specified type from all builds
      const allComponents: PCComponent[] = [];
      builds.forEach(build => {
        const buildComponents = build.components || [];
        const componentsOfType = buildComponents.filter(
          (component: PCComponent) => component.type === componentType
        );
        allComponents.push(...componentsOfType);
      });

      // Remove duplicates based on name, brand, and model
      const uniqueComponents = allComponents.filter((component, index, self) =>
        index === self.findIndex(c =>
          c.name === component.name &&
          c.brand === component.brand &&
          c.model === component.model
        )
      );

      // Get processed components
      const collectionName = `affiliate-${componentType.toLowerCase().replace(' ', '-')}`;
      const processedComponents = await FirestoreService.queryDocuments(collectionName, []) as ComponentAffiliateLinks[];

      // Filter out components that have already been processed
      const unprocessedComponents = uniqueComponents.filter(component =>
        !processedComponents.some(processed =>
          processed.name === component.name &&
          processed.brand === component.brand &&
          processed.model === component.model
        )
      );

      set(state => ({
        unprocessedComponents: {
          ...state.unprocessedComponents,
          [componentType]: unprocessedComponents
        },
        isLoading: false
      }));
    } catch (error) {
      console.error(`Error fetching unprocessed ${componentType} components:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  fetchComponentById: async (componentType: ComponentType, componentId: string) => {
    try {
      set({ isLoading: true, error: null });

      const collectionName = `affiliate-${componentType.toLowerCase().replace(' ', '-')}`;
      const component = await FirestoreService.getDocument(collectionName, componentId);

      set({
        selectedComponent: component as ComponentAffiliateLinks,
        isLoading: false
      });
    } catch (error) {
      console.error(`Error fetching component:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  addComponent: async (component: ComponentAffiliateLinks) => {
    try {
      set({ isSubmitting: true, error: null });

      const collectionName = `affiliate-${component.componentType.toLowerCase().replace(' ', '-')}`;
      const docRef = await FirestoreService.addDocument(collectionName, component);

      // Update the local state
      set(state => {
        const updatedComponents = [...(state.componentsByType[component.componentType] || [])];
        updatedComponents.push({ ...component, id: docRef.id });

        return {
          componentsByType: {
            ...state.componentsByType,
            [component.componentType]: updatedComponents
          },
          isSubmitting: false
        };
      });

      return docRef.id;
    } catch (error) {
      console.error(`Error adding component:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isSubmitting: false
      });
      throw error;
    }
  },

  updateComponent: async (componentId: string, componentData: Partial<ComponentAffiliateLinks>) => {
    try {
      set({ isSubmitting: true, error: null });

      const { selectedComponent } = get();
      if (!selectedComponent) {
        throw new Error('No component selected');
      }

      const collectionName = `affiliate-${selectedComponent.componentType.toLowerCase().replace(' ', '-')}`;
      await FirestoreService.updateDocument(collectionName, componentId, componentData);

      // Update the local state
      set(state => {
        const updatedComponent = { ...selectedComponent, ...componentData, id: componentId };

        // Update in the components list
        const componentsList = [...(state.componentsByType[selectedComponent.componentType] || [])];
        const componentIndex = componentsList.findIndex(c => c.id === componentId);

        if (componentIndex !== -1) {
          componentsList[componentIndex] = updatedComponent;
        }

        return {
          selectedComponent: updatedComponent,
          componentsByType: {
            ...state.componentsByType,
            [selectedComponent.componentType]: componentsList
          },
          isSubmitting: false
        };
      });
    } catch (error) {
      console.error(`Error updating component:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isSubmitting: false
      });
    }
  },

  addCountryToComponent: async (componentId: string, country: CountryCode) => {
    try {
      set({ isSubmitting: true, error: null });

      const { selectedComponent } = get();
      if (!selectedComponent) {
        throw new Error('No component selected');
      }

      // Check if country already exists
      if (selectedComponent.countryLinks.some(c => c.countryCode === country)) {
        throw new Error(`Country ${country} already exists for this component`);
      }

      // Add country to component
      const updatedCountryLinks = [
        ...selectedComponent.countryLinks,
        { countryCode: country, links: [] }
      ];

      // Update in Firestore
      const collectionName = `affiliate-${selectedComponent.componentType.toLowerCase().replace(' ', '-')}`;
      await FirestoreService.updateDocument(collectionName, componentId, {
        countryLinks: updatedCountryLinks,
        updatedAt: new Date()
      });

      // Update local state
      set(state => {
        const updatedComponent = {
          ...selectedComponent,
          countryLinks: updatedCountryLinks,
          updatedAt: new Date()
        };

        // Update in the components list
        const componentsList = [...(state.componentsByType[selectedComponent.componentType] || [])];
        const componentIndex = componentsList.findIndex(c => c.id === componentId);

        if (componentIndex !== -1) {
          componentsList[componentIndex] = updatedComponent;
        }

        return {
          selectedComponent: updatedComponent,
          componentsByType: {
            ...state.componentsByType,
            [selectedComponent.componentType]: componentsList
          },
          isSubmitting: false
        };
      });
    } catch (error) {
      console.error(`Error adding country to component:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isSubmitting: false
      });
    }
  },

  removeCountryFromComponent: async (componentId: string, country: CountryCode) => {
    try {
      set({ isSubmitting: true, error: null });

      const { selectedComponent } = get();
      if (!selectedComponent) {
        throw new Error('No component selected');
      }

      // Remove country from component
      const updatedCountryLinks = selectedComponent.countryLinks.filter(
        c => c.countryCode !== country
      );

      // Update in Firestore
      const collectionName = `affiliate-${selectedComponent.componentType.toLowerCase().replace(' ', '-')}`;
      await FirestoreService.updateDocument(collectionName, componentId, {
        countryLinks: updatedCountryLinks,
        updatedAt: new Date()
      });

      // Update local state
      set(state => {
        const updatedComponent = {
          ...selectedComponent,
          countryLinks: updatedCountryLinks,
          updatedAt: new Date()
        };

        // Update in the components list
        const componentsList = [...(state.componentsByType[selectedComponent.componentType] || [])];
        const componentIndex = componentsList.findIndex(c => c.id === componentId);

        if (componentIndex !== -1) {
          componentsList[componentIndex] = updatedComponent;
        }

        return {
          selectedComponent: updatedComponent,
          componentsByType: {
            ...state.componentsByType,
            [selectedComponent.componentType]: componentsList
          },
          isSubmitting: false
        };
      });
    } catch (error) {
      console.error(`Error removing country from component:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isSubmitting: false
      });
    }
  },

  addAffiliateLinkToCountry: async (componentId: string, country: CountryCode, link: AffiliateLink) => {
    try {
      set({ isSubmitting: true, error: null });

      const { selectedComponent } = get();
      if (!selectedComponent) {
        throw new Error('No component selected');
      }

      // Find country in component
      const countryIndex = selectedComponent.countryLinks.findIndex(
        c => c.countryCode === country
      );

      if (countryIndex === -1) {
        throw new Error(`Country ${country} not found for this component`);
      }

      // Check if marketplace already exists
      if (selectedComponent.countryLinks[countryIndex].links.some(l => l.marketplace === link.marketplace)) {
        throw new Error(`Marketplace ${link.marketplace} already exists for country ${country}`);
      }

      // Add link to country
      const updatedCountryLinks = [...selectedComponent.countryLinks];
      updatedCountryLinks[countryIndex] = {
        ...updatedCountryLinks[countryIndex],
        links: [...updatedCountryLinks[countryIndex].links, { ...link, createdAt: new Date(), updatedAt: new Date() }]
      };

      // Update in Firestore
      const collectionName = `affiliate-${selectedComponent.componentType.toLowerCase().replace(' ', '-')}`;
      await FirestoreService.updateDocument(collectionName, componentId, {
        countryLinks: updatedCountryLinks,
        updatedAt: new Date()
      });

      // Update local state
      set(state => {
        const updatedComponent = {
          ...selectedComponent,
          countryLinks: updatedCountryLinks,
          updatedAt: new Date()
        };

        // Update in the components list
        const componentsList = [...(state.componentsByType[selectedComponent.componentType] || [])];
        const componentIndex = componentsList.findIndex(c => c.id === componentId);

        if (componentIndex !== -1) {
          componentsList[componentIndex] = updatedComponent;
        }

        return {
          selectedComponent: updatedComponent,
          componentsByType: {
            ...state.componentsByType,
            [selectedComponent.componentType]: componentsList
          },
          isSubmitting: false
        };
      });
    } catch (error) {
      console.error(`Error adding affiliate link to country:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isSubmitting: false
      });
    }
  },

  updateAffiliateLink: async (componentId: string, country: CountryCode, marketplace: MarketplaceType, linkData: Partial<AffiliateLink>) => {
    try {
      set({ isSubmitting: true, error: null });

      const { selectedComponent } = get();
      if (!selectedComponent) {
        throw new Error('No component selected');
      }

      // Find country in component
      const countryIndex = selectedComponent.countryLinks.findIndex(
        c => c.countryCode === country
      );

      if (countryIndex === -1) {
        throw new Error(`Country ${country} not found for this component`);
      }

      // Find link in country
      const linkIndex = selectedComponent.countryLinks[countryIndex].links.findIndex(
        l => l.marketplace === marketplace
      );

      if (linkIndex === -1) {
        throw new Error(`Marketplace ${marketplace} not found for country ${country}`);
      }

      // Update link
      const updatedCountryLinks = [...selectedComponent.countryLinks];
      updatedCountryLinks[countryIndex] = {
        ...updatedCountryLinks[countryIndex],
        links: [...updatedCountryLinks[countryIndex].links]
      };

      updatedCountryLinks[countryIndex].links[linkIndex] = {
        ...updatedCountryLinks[countryIndex].links[linkIndex],
        ...linkData,
        updatedAt: new Date()
      };

      // Update in Firestore
      const collectionName = `affiliate-${selectedComponent.componentType.toLowerCase().replace(' ', '-')}`;
      await FirestoreService.updateDocument(collectionName, componentId, {
        countryLinks: updatedCountryLinks,
        updatedAt: new Date()
      });

      // Update local state
      set(state => {
        const updatedComponent = {
          ...selectedComponent,
          countryLinks: updatedCountryLinks,
          updatedAt: new Date()
        };

        // Update in the components list
        const componentsList = [...(state.componentsByType[selectedComponent.componentType] || [])];
        const componentIndex = componentsList.findIndex(c => c.id === componentId);

        if (componentIndex !== -1) {
          componentsList[componentIndex] = updatedComponent;
        }

        return {
          selectedComponent: updatedComponent,
          componentsByType: {
            ...state.componentsByType,
            [selectedComponent.componentType]: componentsList
          },
          isSubmitting: false
        };
      });
    } catch (error) {
      console.error(`Error updating affiliate link:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isSubmitting: false
      });
    }
  },

  removeAffiliateLink: async (componentId: string, country: CountryCode, marketplace: MarketplaceType) => {
    try {
      set({ isSubmitting: true, error: null });

      const { selectedComponent } = get();
      if (!selectedComponent) {
        throw new Error('No component selected');
      }

      // Find country in component
      const countryIndex = selectedComponent.countryLinks.findIndex(
        c => c.countryCode === country
      );

      if (countryIndex === -1) {
        throw new Error(`Country ${country} not found for this component`);
      }

      // Remove link from country
      const updatedCountryLinks = [...selectedComponent.countryLinks];
      updatedCountryLinks[countryIndex] = {
        ...updatedCountryLinks[countryIndex],
        links: updatedCountryLinks[countryIndex].links.filter(l => l.marketplace !== marketplace)
      };

      // Update in Firestore
      const collectionName = `affiliate-${selectedComponent.componentType.toLowerCase().replace(' ', '-')}`;
      await FirestoreService.updateDocument(collectionName, componentId, {
        countryLinks: updatedCountryLinks,
        updatedAt: new Date()
      });

      // Update local state
      set(state => {
        const updatedComponent = {
          ...selectedComponent,
          countryLinks: updatedCountryLinks,
          updatedAt: new Date()
        };

        // Update in the components list
        const componentsList = [...(state.componentsByType[selectedComponent.componentType] || [])];
        const componentIndex = componentsList.findIndex(c => c.id === componentId);

        if (componentIndex !== -1) {
          componentsList[componentIndex] = updatedComponent;
        }

        return {
          selectedComponent: updatedComponent,
          componentsByType: {
            ...state.componentsByType,
            [selectedComponent.componentType]: componentsList
          },
          isSubmitting: false
        };
      });
    } catch (error) {
      console.error(`Error removing affiliate link:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isSubmitting: false
      });
    }
  },

  clearSelectedComponent: () => {
    set({ selectedComponent: null });
  },

  markComponentAsProcessed: async (componentType: ComponentType, componentId: string) => {
    try {
      set({ isSubmitting: true, error: null });

      const collectionName = `affiliate-${componentType.toLowerCase().replace(' ', '-')}`;
      await FirestoreService.updateDocument(collectionName, componentId, {
        isProcessed: true,
        updatedAt: new Date()
      });

      // Update local state
      set(state => {
        // Update in the components list
        const componentsList = [...(state.componentsByType[componentType] || [])];
        const componentIndex = componentsList.findIndex(c => c.id === componentId);

        if (componentIndex !== -1) {
          componentsList[componentIndex] = {
            ...componentsList[componentIndex],
            isProcessed: true,
            updatedAt: new Date()
          };
        }

        // Update selected component if it's the same one
        let updatedSelectedComponent = state.selectedComponent;
        if (state.selectedComponent && state.selectedComponent.id === componentId) {
          updatedSelectedComponent = {
            ...state.selectedComponent,
            isProcessed: true,
            updatedAt: new Date()
          };
        }

        return {
          selectedComponent: updatedSelectedComponent,
          componentsByType: {
            ...state.componentsByType,
            [componentType]: componentsList
          },
          isSubmitting: false
        };
      });
    } catch (error) {
      console.error(`Error marking component as processed:`, error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isSubmitting: false
      });
    }
  }
}));

export default useAdminStore;

'use client';

import { create } from 'zustand';
import { User, ActionCodeSettings } from 'firebase/auth';
import { AuthService } from '@/lib/services/auth-service';
import { AnalyticsService } from '@/lib/services/analytics-service';

export interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  country: string;
  photoURL?: string;
}

interface AuthState {
  // User state
  user: User | null;
  isLoading: boolean;
  error: string | null;

  // Auth methods
  signIn: (email: string, password: string) => Promise<User>;
  signInWithGoogle: () => Promise<User>;
  signUp: (email: string, password: string) => Promise<User>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;

  // Email link auth methods
  sendSignInLink: (email: string, actionCodeSettings: ActionCodeSettings) => Promise<void>;
  signInWithEmailLink: (email: string, link: string) => Promise<User>;
  isSignInWithEmailLink: (url: string) => boolean;

  // User profile methods
  updateUserProfile: (profile: Partial<UserProfile>) => Promise<void>;

  // Initialize auth state
  initAuth: () => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  // User state
  user: null,
  isLoading: true,
  error: null,

  // Auth methods
  signIn: async (email, password) => {
    set({ isLoading: true, error: null });

    try {
      const result = await AuthService.signInWithEmailAndPassword(email, password);
      set({ user: result.user, isLoading: false });

      // Set user ID for analytics
      if (result.user) {
        AnalyticsService.setUserId(result.user.uid);
        AnalyticsService.logEvent('login', { method: 'email' });
      }

      return result.user;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  signInWithGoogle: async () => {
    set({ isLoading: true, error: null });

    try {
      const result = await AuthService.signInWithGoogle();
      set({ user: result.user, isLoading: false });

      // Set user ID for analytics
      if (result.user) {
        AnalyticsService.setUserId(result.user.uid);
        AnalyticsService.logEvent('login', { method: 'google' });
      }

      return result.user;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  signUp: async (email, password) => {
    set({ isLoading: true, error: null });

    try {
      const result = await AuthService.registerWithEmailAndPassword(email, password);
      set({ user: result.user, isLoading: false });

      // Set user ID for analytics
      if (result.user) {
        AnalyticsService.setUserId(result.user.uid);
        AnalyticsService.logEvent('sign_up', { method: 'email' });
      }

      return result.user;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  signOut: async () => {
    set({ isLoading: true, error: null });

    try {
      await AuthService.signOut();
      set({ user: null, isLoading: false });

      // Clear user ID for analytics
      AnalyticsService.setUserId('');
      AnalyticsService.logEvent('logout');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  resetPassword: async (email) => {
    set({ isLoading: true, error: null });

    try {
      await AuthService.sendPasswordResetEmail(email);
      set({ isLoading: false });

      AnalyticsService.logEvent('password_reset');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  // Email link auth methods
  sendSignInLink: async (email, actionCodeSettings) => {
    set({ isLoading: true, error: null });

    try {
      await AuthService.sendSignInLinkToEmail(email, actionCodeSettings);
      set({ isLoading: false });

      AnalyticsService.logEvent('email_link_sent');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  signInWithEmailLink: async (email, link) => {
    set({ isLoading: true, error: null });

    try {
      const result = await AuthService.signInWithEmailLink(email, link);
      set({ user: result.user, isLoading: false });

      // Set user ID for analytics
      if (result.user) {
        AnalyticsService.setUserId(result.user.uid);
        AnalyticsService.logEvent('login', { method: 'email_link' });
      }

      return result.user;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  isSignInWithEmailLink: (url) => {
    return AuthService.isSignInWithEmailLink(url);
  },

  // User profile methods
  updateUserProfile: async (profile) => {
    set({ isLoading: true, error: null });

    try {
      const user = get().user;
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Update display name with first and last name
      if (profile.firstName && profile.lastName) {
        await AuthService.updateUserProfile(user, {
          displayName: `${profile.firstName} ${profile.lastName}`,
          photoURL: profile.photoURL || user.photoURL
        });
      } else if (profile.photoURL) {
        await AuthService.updateUserProfile(user, {
          photoURL: profile.photoURL
        });
      }

      // Force refresh the user object
      set({ user: { ...user } });

      AnalyticsService.logEvent('profile_updated');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  // Initialize auth state
  initAuth: () => {
    const unsubscribe = AuthService.onAuthStateChanged((user) => {
      set({ user, isLoading: false });

      // Set user ID for analytics if user is logged in
      if (user) {
        AnalyticsService.setUserId(user.uid);
      }
    });

    // Return unsubscribe function for cleanup
    return unsubscribe;
  }
}));

export default useAuthStore;

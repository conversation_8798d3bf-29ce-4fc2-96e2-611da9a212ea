'use client';

import { getStorage, ref, uploadString, getDownloadURL, deleteObject } from 'firebase/storage';
import { app } from './firebase-config';

// Initialize Firebase Storage
const storage = getStorage(app);

/**
 * Firebase Storage Service
 * Provides methods for file storage and retrieval
 */
export class StorageService {
  /**
   * Upload a file as a data URL
   * @param path Path to store the file
   * @param dataUrl Data URL of the file
   * @returns Promise with download URL
   */
  static async uploadDataUrl(path: string, dataUrl: string): Promise<string> {
    try {
      // Create a reference to the file location
      const fileRef = ref(storage, path);
      
      // Upload the data URL
      const snapshot = await uploadString(fileRef, dataUrl, 'data_url');
      
      // Get the download URL
      const downloadUrl = await getDownloadURL(snapshot.ref);
      
      return downloadUrl;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  /**
   * Upload a profile image
   * @param userId User ID
   * @param dataUrl Data URL of the image
   * @returns Promise with download URL
   */
  static async uploadProfileImage(userId: string, dataUrl: string): Promise<string> {
    // Generate a unique path for the profile image
    const path = `profile-images/${userId}/${Date.now()}.jpg`;
    return this.uploadDataUrl(path, dataUrl);
  }

  /**
   * Delete a file
   * @param url URL of the file to delete
   * @returns Promise that resolves when deletion is complete
   */
  static async deleteFile(url: string): Promise<void> {
    try {
      // Extract the path from the URL
      const fileRef = ref(storage, this.getPathFromUrl(url));
      
      // Delete the file
      await deleteObject(fileRef);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  }

  /**
   * Get the storage path from a download URL
   * @param url Download URL
   * @returns Storage path
   */
  private static getPathFromUrl(url: string): string {
    // Extract the path from the URL
    // This is a simple implementation and might need to be adjusted
    const baseUrl = 'https://firebasestorage.googleapis.com/v0/b/';
    const pathStart = url.indexOf(baseUrl) + baseUrl.length;
    const pathEnd = url.indexOf('?');
    const fullPath = url.substring(pathStart, pathEnd);
    
    // Remove the bucket name and 'o/' prefix
    const parts = fullPath.split('/o/');
    if (parts.length > 1) {
      return decodeURIComponent(parts[1]);
    }
    
    return '';
  }
}

export default StorageService;

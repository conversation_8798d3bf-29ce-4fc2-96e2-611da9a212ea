'use client';

import {
  getAuth,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User,
  GoogleAuthProvider,
  signInWithPopup,
  sendPasswordResetEmail,
  sendSignInLinkToEmail as firebaseSendSignInLinkToEmail,
  isSignInWithEmailLink as firebaseIsSignInWithEmailLink,
  signInWithEmailLink as firebaseSignInWithEmailLink,
  updateProfile,
  ActionCodeSettings,
  UserCredential,
} from "firebase/auth";
import { app } from "./firebase-config";

// Initialize Firebase Auth
const auth = getAuth(app);

/**
 * Firebase Authentication Service
 * Provides methods for user authentication and management
 */
export class AuthService {
  /**
   * Register a new user with email and password
   * @param email User's email
   * @param password User's password
   * @returns Promise with user credentials
   */
  static async registerWithEmailAndPassword(email: string, password: string) {
    try {
      return await createUserWith<PERSON>mailAndPassword(auth, email, password);
    } catch (error) {
      console.error("Error registering user:", error);
      throw error;
    }
  }

  /**
   * Sign in an existing user with email and password
   * @param email User's email
   * @param password User's password
   * @returns Promise with user credentials
   */
  static async signInWithEmailAndPassword(email: string, password: string) {
    try {
      return await signInWithEmailAndPassword(auth, email, password);
    } catch (error) {
      console.error("Error signing in user:", error);
      throw error;
    }
  }

  /**
   * Send a sign-in link to the user's email
   * @param email User's email
   * @param actionCodeSettings Settings for the email link
   * @returns Promise that resolves when email is sent
   */
  static async sendSignInLinkToEmail(email: string, actionCodeSettings: ActionCodeSettings) {
    try {
      return await firebaseSendSignInLinkToEmail(auth, email, actionCodeSettings);
    } catch (error) {
      console.error("Error sending sign-in link:", error);
      throw error;
    }
  }

  /**
   * Check if the current URL is a sign-in with email link
   * @param url The URL to check
   * @returns True if the URL is a sign-in with email link
   */
  static isSignInWithEmailLink(url: string): boolean {
    return firebaseIsSignInWithEmailLink(auth, url);
  }

  /**
   * Sign in with an email link
   * @param email User's email
   * @param emailLink The email link
   * @returns Promise with user credentials
   */
  static async signInWithEmailLink(email: string, emailLink: string): Promise<UserCredential> {
    try {
      return await firebaseSignInWithEmailLink(auth, email, emailLink);
    } catch (error) {
      console.error("Error signing in with email link:", error);
      throw error;
    }
  }

  /**
   * Update user profile
   * @param user User to update
   * @param profile Profile data to update
   * @returns Promise that resolves when profile is updated
   */
  static async updateUserProfile(user: User, profile: { displayName?: string | null; photoURL?: string | null }) {
    try {
      return await updateProfile(user, profile);
    } catch (error) {
      console.error("Error updating user profile:", error);
      throw error;
    }
  }

  /**
   * Sign in with Google
   * @returns Promise with user credentials
   */
  static async signInWithGoogle() {
    try {
      const provider = new GoogleAuthProvider();
      return await signInWithPopup(auth, provider);
    } catch (error) {
      console.error("Error signing in with Google:", error);
      throw error;
    }
  }

  /**
   * Sign out the current user
   * @returns Promise that resolves when sign out is complete
   */
  static async signOut() {
    try {
      return await signOut(auth);
    } catch (error) {
      console.error("Error signing out:", error);
      throw error;
    }
  }

  /**
   * Send a password reset email
   * @param email User's email
   * @returns Promise that resolves when email is sent
   */
  static async sendPasswordResetEmail(email: string) {
    try {
      return await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error("Error sending password reset email:", error);
      throw error;
    }
  }

  /**
   * Get the current user
   * @returns Current user or null if not signed in
   */
  static getCurrentUser(): User | null {
    return auth.currentUser;
  }

  /**
   * Subscribe to auth state changes
   * @param callback Function to call when auth state changes
   * @returns Unsubscribe function
   */
  static onAuthStateChanged(callback: (user: User | null) => void) {
    return onAuthStateChanged(auth, callback);
  }
}

export default AuthService;

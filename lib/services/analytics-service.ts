'use client';

import { Analytics, getAnalytics, logEvent, setUserId, setUserProperties } from "firebase/analytics";
import { app } from "./firebase-config";

// Initialize Analytics (only in browser environment)
let analytics: Analytics | null = null;
if (typeof window !== 'undefined') {
  analytics = getAnalytics(app);
}

/**
 * Firebase Analytics Service
 * Provides methods for tracking user events and properties
 */
export class AnalyticsService {
  /**
   * Log an event
   * @param eventName Name of the event
   * @param eventParams Optional event parameters
   */
  static logEvent(eventName: string, eventParams?: Record<string, unknown>) {
    if (typeof window !== 'undefined' && analytics) {
      logEvent(analytics, eventName, eventParams);
    } else {
      console.warn('Analytics not available in this environment');
    }
  }

  /**
   * Set user ID for analytics
   * @param userId User ID
   */
  static setUserId(userId: string) {
    if (typeof window !== 'undefined' && analytics) {
      setUserId(analytics, userId);
    } else {
      console.warn('Analytics not available in this environment');
    }
  }

  /**
   * Set user properties for analytics
   * @param properties User properties
   */
  static setUserProperties(properties: Record<string, unknown>) {
    if (typeof window !== 'undefined' && analytics) {
      setUserProperties(analytics, properties);
    } else {
      console.warn('Analytics not available in this environment');
    }
  }

  /**
   * Log page view event
   * @param pageName Name of the page
   * @param pageParams Additional page parameters
   */
  static logPageView(pageName: string, pageParams?: Record<string, unknown>) {
    this.logEvent('page_view', {
      page_title: pageName,
      ...pageParams
    });
  }

  /**
   * Log build creation event
   * @param buildId Build ID
   * @param buildType Type of build (e.g., gaming, content creation)
   * @param buildBudget Budget range
   */
  static logBuildCreation(buildId: string, buildType: string, buildBudget: string) {
    this.logEvent('build_created', {
      build_id: buildId,
      build_type: buildType,
      build_budget: buildBudget
    });
  }

  /**
   * Log build save event
   * @param buildId Build ID
   */
  static logBuildSave(buildId: string) {
    this.logEvent('build_saved', {
      build_id: buildId
    });
  }

  /**
   * Log build share event
   * @param buildId Build ID
   * @param shareMethod Method of sharing (e.g., email, social)
   */
  static logBuildShare(buildId: string, shareMethod: string) {
    this.logEvent('build_shared', {
      build_id: buildId,
      share_method: shareMethod
    });
  }

  /**
   * Log component selection event
   * @param componentType Type of component
   * @param componentName Name of component
   * @param buildId Associated build ID
   */
  static logComponentSelection(componentType: string, componentName: string, buildId: string) {
    this.logEvent('component_selected', {
      component_type: componentType,
      component_name: componentName,
      build_id: buildId
    });
  }

  /**
   * Log chat interaction event
   * @param messageCount Number of messages in the conversation
   * @param buildId Associated build ID
   */
  static logChatInteraction(messageCount: number, buildId: string) {
    this.logEvent('chat_interaction', {
      message_count: messageCount,
      build_id: buildId
    });
  }
}

export default AnalyticsService;

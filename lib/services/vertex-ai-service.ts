'use client';

import { GoogleGenA<PERSON>, Type, Schema } from "@google/genai";
import { PCBuild, RecommendedBuilds } from "@/types/pc-build";

const GEMINI_API_KEY = process.env.NEXT_PUBLIC_GEMINI_API_KEY;

const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

// Cache for recommended builds to prevent duplicate API calls
let recommendedBuildsCache: RecommendedBuilds | null = null;
let lastFetchTime = 0;
const CACHE_EXPIRY_TIME = 1000 * 60 * 60 * 24; // 24 hours in milliseconds

// Define the PC build schema for structured output
const pcBuildSchema: Schema = {
  type: Type.OBJECT,
  properties: {
    name: { type: Type.STRING },
    description: { type: Type.STRING },
    totalPrice: { type: Type.NUMBER },
    useCase: { type: Type.STRING, format: "enum", enum: ["gaming", "content", "work", "everyday use"] },
    budget: { type: Type.STRING, format: "enum", enum: ["$500-$800", "$800-$1500", "$1500-$2500", "$400-$600", "$600-$1000", "$1000-$1500"] },
    components: {
      type: Type.ARRAY,
      items: {
        type: Type.OBJECT,
        properties: {
          type: { type: Type.STRING, format: "enum", enum: ["CPU", "GPU", "RAM", "Motherboard", "SSD", "HDD", "PSU", "Case", "CPU Cooler"] },
          name: { type: Type.STRING },
          brand: { type: Type.STRING },
          model: { type: Type.STRING },
          price: { type: Type.NUMBER },
          specs: {
            type: Type.ARRAY,
            items: {
              type: Type.OBJECT,
              properties: {
                name: { type: Type.STRING },
                value: { type: Type.STRING },
              },
            },
            minItems: "0",
            maxItems: "5",
          }
        },
        required: ["type", "name", "brand", "model", "price", "specs"]
      }
    },
    compatibility: {
      type: Type.OBJECT,
      properties: {
        isCompatible: { type: Type.BOOLEAN },
        issues: {
          type: Type.ARRAY,
          items: { type: Type.STRING }
        }
      },
      required: ["isCompatible", "issues"]
    }
  },
  required: ["name", "description", "totalPrice", "useCase", "budget", "components", "compatibility"]
};

/**
 * Vertex AI Service
 * Provides methods for interacting with Vertex AI
 */
export class VertexAIService {
  /**
   * Start a chat session
   * @param systemPromptContent Optional system prompt content
   * @returns Chat session
   */
  static startChat(systemPromptContent?: string) {
    if (!ai) {
      console.error("Gemini AI is not initialized");
      throw new Error("Gemini AI is not initialized");
    }

    console.log("Starting new chat session");

    // Create a chat session with an empty history
    // We'll add messages after creation
    const chatSession = ai.chats.create({
      model: "gemini-2.5-flash-preview-05-20",
      history: [
        { role: "user", parts: [{ text: "Hello" }] },
        { role: "model", parts: [{ text: "Hello! How can I help you today?" }] }
      ],
      config: {
        systemInstruction: systemPromptContent,
        maxOutputTokens: 5000,
        temperature: 1,
        topP: 0.2,
        topK: 40,
        thinkingConfig: {
          includeThoughts: false,
          thinkingBudget: 0,
        }
      }
    });

    return chatSession;
  }

  /**
   * Generate a PC build recommendation with structured output
   * @param useCase Primary use case for the PC
   * @param budget Budget range
   * @param requirements Additional requirements
   * @returns Structured PC build recommendation
   */
  static async generateBuildRecommendation(
    useCase: string,
    budget: string,
    requirements?: string
  ): Promise<PCBuild> {
    try {
      // Check if Gemini AI is initialized
      if (!ai) {
        console.error("Gemini AI is not initialized");
        return this.getDefaultBuild(useCase, budget, requirements);
      }

      // Construct a detailed prompt for the build recommendation
      const prompt = `
I need a PC build recommendation with the following requirements:
- Primary use case: ${useCase}
- Budget: ${budget}
${requirements ? `- Additional requirements: ${requirements}` : ''}

Please provide a complete build with all necessary components (CPU, GPU, RAM, Motherboard, SSD, HDD, PSU, Case, CPU Cooler if needed).
For each component, include the brand, model, price, and key specifications.
Ensure all components are compatible with each other.
Explain why you chose these specific components for my use case.
Format your response as structured JSON that I can parse programmatically.
Make sure the JSON is valid and does not contain any line breaks or \\n within property values.
Use a compact format without extra whitespace or newlines between properties.
Example format:
{"name": "Gaming PC","description": "High-performance gaming build","components": [{"type": "CPU", "name": "AMD Ryzen 7", "price": 300}]}`;

      const systemPrompt = `You are DIYPC, an AI assistant specialized in helping users build custom PCs.
Your primary role is to help users create PC builds based on their requirements.
You will be provided with a detailed prompt that includes the user's needs, such as the primary use case, budget, and any additional requirements.
Your task is to generate a complete PC build recommendation that meets all the specified requirements.
You will need to consider the user's budget and requirements to ensure the PC meets their needs.
You will also need to consider compatibility between components to ensure they work together.
`;

      // Call the model with the prompt
      const result = await ai.models.generateContent({
        model: "gemini-2.5-flash-preview-05-20",
        contents: prompt,
        config: {
          systemInstruction: {
            role: "system",
            parts: [{ text: systemPrompt }]
          },
          responseMimeType: "application/json",
          responseSchema: pcBuildSchema,
          temperature: 0.5,
          maxOutputTokens: 10000,
          thinkingConfig: {
            includeThoughts: true,
            thinkingBudget: 2000,
          }
        }
      });
      const response = result.text;

      // Parse the JSON response
      if (response) {
        const buildData = JSON.parse(response) as PCBuild;
        return buildData;
      }
      return this.getDefaultBuild(useCase, budget, requirements);
    } catch (error) {
      console.error("Error generating build recommendation:", error);
      return this.getDefaultBuild(useCase, budget, requirements);
    }
  }

  /**
   * Get a default build when AI generation fails
   * @param useCase Primary use case
   * @param budget Budget range
   * @param requirements Additional requirements
   * @returns Default PC build
   */
  static getDefaultBuild(useCase: string, budget: string, requirements?: string): PCBuild {
    // Select a default build based on use case and budget
    let defaultBuild: PCBuild;

    if (useCase === 'gaming') {
      if (budget.includes('500') || budget.includes('800')) {
        defaultBuild = this.getDefaultPreConfiguredBuilds().gaming[0];
      } else {
        defaultBuild = {
          name: 'Mid-range Gaming PC',
          description: 'Great for 1440p gaming at high settings',
          totalPrice: 1200,
          useCase: 'gaming',
          budget: '$800-$1500',
          components: [
            { type: 'CPU', name: 'AMD Ryzen 7 5800X', brand: 'AMD', model: 'Ryzen 7 5800X', price: 299, specs: [] },
            { type: 'GPU', name: 'NVIDIA RTX 3070', brand: 'NVIDIA', model: 'RTX 3070', price: 499, specs: [] },
            { type: 'RAM', name: '16GB DDR4 3600MHz', brand: 'Corsair', model: 'Vengeance RGB Pro', price: 89, specs: [] },
            { type: 'Motherboard', name: 'MSI MAG B550 TOMAHAWK', brand: 'MSI', model: 'MAG B550 TOMAHAWK', price: 179, specs: [] },
            { type: 'SSD', name: '1TB NVMe SSD', brand: 'Samsung', model: '970 EVO Plus', price: 129, specs: [] },
            { type: 'PSU', name: '750W 80+ Gold', brand: 'Corsair', model: 'RM750x', price: 119, specs: [] },
            { type: 'Case', name: 'Corsair 4000D Airflow', brand: 'Corsair', model: '4000D Airflow', price: 89, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        };
      }
    } else if (useCase === 'content') {
      defaultBuild = this.getDefaultPreConfiguredBuilds().content[0];
    } else {
      defaultBuild = this.getDefaultPreConfiguredBuilds().work[0];
    }

    // Update with user requirements
    defaultBuild.requirements = requirements;

    return defaultBuild;
  }

  /**
   * Get default pre-configured builds
   * @returns Recommended builds by category
   */
  static getDefaultPreConfiguredBuilds(): RecommendedBuilds {
    return {
      gaming: [
        {
          name: 'Entry-level Gaming PC',
          description: 'Great for 1080p gaming at medium to high settings',
          totalPrice: 800,
          useCase: 'gaming',
          budget: '$500-$800',
          components: [
            { type: 'CPU', name: 'AMD Ryzen 5 5600X', brand: 'AMD', model: 'Ryzen 5 5600X', price: 199, specs: [] },
            { type: 'GPU', name: 'NVIDIA RTX 3060', brand: 'NVIDIA', model: 'RTX 3060', price: 329, specs: [] },
            { type: 'RAM', name: '16GB DDR4 3200MHz', brand: 'Corsair', model: 'Vengeance LPX', price: 69, specs: [] },
            { type: 'Motherboard', name: 'MSI B550-A PRO', brand: 'MSI', model: 'B550-A PRO', price: 139, specs: [] },
            { type: 'SSD', name: '1TB NVMe SSD', brand: 'Samsung', model: '970 EVO', price: 99, specs: [] },
            { type: 'PSU', name: '650W 80+ Bronze', brand: 'EVGA', model: 'BQ 650W', price: 69, specs: [] },
            { type: 'Case', name: 'NZXT H510', brand: 'NZXT', model: 'H510', price: 69, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        },
        {
          name: 'Mid-range Gaming PC',
          description: 'Great for 1440p gaming at high settings',
          totalPrice: 1200,
          useCase: 'gaming',
          budget: '$800-$1500',
          components: [
            { type: 'CPU', name: 'AMD Ryzen 7 5800X', brand: 'AMD', model: 'Ryzen 7 5800X', price: 299, specs: [] },
            { type: 'GPU', name: 'NVIDIA RTX 3070', brand: 'NVIDIA', model: 'RTX 3070', price: 499, specs: [] },
            { type: 'RAM', name: '16GB DDR4 3600MHz', brand: 'Corsair', model: 'Vengeance RGB Pro', price: 89, specs: [] },
            { type: 'Motherboard', name: 'MSI MAG B550 TOMAHAWK', brand: 'MSI', model: 'MAG B550 TOMAHAWK', price: 179, specs: [] },
            { type: 'SSD', name: '1TB NVMe SSD', brand: 'Samsung', model: '970 EVO Plus', price: 129, specs: [] },
            { type: 'PSU', name: '750W 80+ Gold', brand: 'Corsair', model: 'RM750x', price: 119, specs: [] },
            { type: 'Case', name: 'Corsair 4000D Airflow', brand: 'Corsair', model: '4000D Airflow', price: 89, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        },
        {
          name: 'High-end Gaming PC',
          description: 'Ultimate gaming experience at 4K resolution',
          totalPrice: 2200,
          useCase: 'gaming',
          budget: '$1500-$2500',
          components: [
            { type: 'CPU', name: 'AMD Ryzen 9 5950X', brand: 'AMD', model: 'Ryzen 9 5950X', price: 549, specs: [] },
            { type: 'GPU', name: 'NVIDIA RTX 3080 Ti', brand: 'NVIDIA', model: 'RTX 3080 Ti', price: 999, specs: [] },
            { type: 'RAM', name: '32GB DDR4 3600MHz', brand: 'G.Skill', model: 'Trident Z Neo', price: 169, specs: [] },
            { type: 'Motherboard', name: 'ASUS ROG Strix X570-E', brand: 'ASUS', model: 'ROG Strix X570-E', price: 299, specs: [] },
            { type: 'SSD', name: '2TB NVMe SSD', brand: 'Samsung', model: '980 PRO', price: 229, specs: [] },
            { type: 'PSU', name: '850W 80+ Platinum', brand: 'Corsair', model: 'HX850', price: 169, specs: [] },
            { type: 'Case', name: 'Lian Li PC-O11 Dynamic', brand: 'Lian Li', model: 'PC-O11 Dynamic', price: 149, specs: [] },
            { type: 'CPU Cooler', name: 'NZXT Kraken X73', brand: 'NZXT', model: 'Kraken X73', price: 179, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        }
      ],
      content: [
        {
          name: 'Entry-level Content Creation PC',
          description: 'Affordable setup for basic video editing and design',
          totalPrice: 1000,
          useCase: 'content',
          budget: '$800-$1200',
          components: [
            { type: 'CPU', name: 'AMD Ryzen 7 5700X', brand: 'AMD', model: 'Ryzen 7 5700X', price: 259, specs: [] },
            { type: 'GPU', name: 'NVIDIA RTX 3060', brand: 'NVIDIA', model: 'RTX 3060', price: 329, specs: [] },
            { type: 'RAM', name: '32GB DDR4 3200MHz', brand: 'Corsair', model: 'Vengeance LPX', price: 129, specs: [] },
            { type: 'Motherboard', name: 'MSI B550-A PRO', brand: 'MSI', model: 'B550-A PRO', price: 139, specs: [] },
            { type: 'SSD', name: '1TB NVMe SSD', brand: 'Samsung', model: '970 EVO', price: 99, specs: [] },
            { type: 'PSU', name: '650W 80+ Gold', brand: 'EVGA', model: 'SuperNOVA 650 G5', price: 89, specs: [] },
            { type: 'Case', name: 'Fractal Design Focus G', brand: 'Fractal Design', model: 'Focus G', price: 59, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        },
        {
          name: 'Mid-range Content Creation PC',
          description: 'Optimized for video editing and graphic design',
          totalPrice: 1500,
          useCase: 'content',
          budget: '$1200-$2000',
          components: [
            { type: 'CPU', name: 'AMD Ryzen 9 5900X', brand: 'AMD', model: 'Ryzen 9 5900X', price: 499, specs: [] },
            { type: 'GPU', name: 'NVIDIA RTX 3070', brand: 'NVIDIA', model: 'RTX 3070', price: 499, specs: [] },
            { type: 'RAM', name: '32GB DDR4 3600MHz', brand: 'G.Skill', model: 'Trident Z', price: 159, specs: [] },
            { type: 'Motherboard', name: 'ASUS TUF X570-Plus', brand: 'ASUS', model: 'TUF X570-Plus', price: 189, specs: [] },
            { type: 'SSD', name: '2TB NVMe SSD', brand: 'Samsung', model: '980 PRO', price: 229, specs: [] },
            { type: 'PSU', name: '750W 80+ Gold', brand: 'Corsair', model: 'RM750x', price: 129, specs: [] },
            { type: 'Case', name: 'Fractal Design Meshify C', brand: 'Fractal Design', model: 'Meshify C', price: 99, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        },
        {
          name: 'Professional Content Creation Workstation',
          description: 'High-end workstation for professional video production and 3D rendering',
          totalPrice: 2800,
          useCase: 'content',
          budget: '$2000-$3000',
          components: [
            { type: 'CPU', name: 'AMD Ryzen Threadripper 3960X', brand: 'AMD', model: 'Threadripper 3960X', price: 1399, specs: [] },
            { type: 'GPU', name: 'NVIDIA RTX 3090', brand: 'NVIDIA', model: 'RTX 3090', price: 1499, specs: [] },
            { type: 'RAM', name: '64GB DDR4 3600MHz', brand: 'G.Skill', model: 'Trident Z Royal', price: 329, specs: [] },
            { type: 'Motherboard', name: 'ASUS ROG Strix TRX40-E', brand: 'ASUS', model: 'ROG Strix TRX40-E', price: 499, specs: [] },
            { type: 'SSD', name: '4TB NVMe SSD', brand: 'Samsung', model: '980 PRO', price: 499, specs: [] },
            { type: 'PSU', name: '1200W 80+ Platinum', brand: 'Corsair', model: 'AX1200i', price: 299, specs: [] },
            { type: 'Case', name: 'Phanteks Enthoo Pro 2', brand: 'Phanteks', model: 'Enthoo Pro 2', price: 199, specs: [] },
            { type: 'CPU Cooler', name: 'NZXT Kraken X73', brand: 'NZXT', model: 'Kraken X73', price: 179, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        }
      ],
      work: [
        {
          name: 'Entry-level Office PC',
          description: 'Affordable PC for everyday office tasks and web browsing',
          totalPrice: 600,
          useCase: 'work',
          budget: '$500-$800',
          components: [
            { type: 'CPU', name: 'Intel Core i5-12400', brand: 'Intel', model: 'Core i5-12400', price: 199, specs: [] },
            { type: 'GPU', name: 'Intel UHD Graphics 730', brand: 'Intel', model: 'UHD Graphics 730', price: 0, specs: [] },
            { type: 'RAM', name: '16GB DDR4 3200MHz', brand: 'Crucial', model: 'Ballistix', price: 69, specs: [] },
            { type: 'Motherboard', name: 'ASRock B660M-HDV', brand: 'ASRock', model: 'B660M-HDV', price: 99, specs: [] },
            { type: 'SSD', name: '512GB NVMe SSD', brand: 'Western Digital', model: 'Blue SN550', price: 59, specs: [] },
            { type: 'PSU', name: '500W 80+ Bronze', brand: 'EVGA', model: 'W1', price: 49, specs: [] },
            { type: 'Case', name: 'Thermaltake Versa H18', brand: 'Thermaltake', model: 'Versa H18', price: 49, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        },
        {
          name: 'Professional Workstation',
          description: 'Reliable performance for office work and productivity',
          totalPrice: 1000,
          useCase: 'work',
          budget: '$800-$1500',
          components: [
            { type: 'CPU', name: 'Intel Core i7-12700', brand: 'Intel', model: 'Core i7-12700', price: 359, specs: [] },
            { type: 'GPU', name: 'NVIDIA RTX 3050', brand: 'NVIDIA', model: 'RTX 3050', price: 249, specs: [] },
            { type: 'RAM', name: '32GB DDR4 3200MHz', brand: 'Crucial', model: 'Ballistix', price: 129, specs: [] },
            { type: 'Motherboard', name: 'MSI MAG B660M', brand: 'MSI', model: 'MAG B660M', price: 149, specs: [] },
            { type: 'SSD', name: '1TB NVMe SSD', brand: 'Western Digital', model: 'SN750', price: 109, specs: [] },
            { type: 'PSU', name: '650W 80+ Gold', brand: 'Seasonic', model: 'FOCUS GX-650', price: 99, specs: [] },
            { type: 'Case', name: 'Cooler Master MasterBox Q300L', brand: 'Cooler Master', model: 'MasterBox Q300L', price: 49, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        },
        {
          name: 'High-Performance Business Workstation',
          description: 'Premium workstation for demanding business applications and multitasking',
          totalPrice: 1800,
          useCase: 'work',
          budget: '$1500-$2000',
          components: [
            { type: 'CPU', name: 'Intel Core i9-12900K', brand: 'Intel', model: 'Core i9-12900K', price: 589, specs: [] },
            { type: 'GPU', name: 'NVIDIA RTX 3060 Ti', brand: 'NVIDIA', model: 'RTX 3060 Ti', price: 399, specs: [] },
            { type: 'RAM', name: '64GB DDR4 3600MHz', brand: 'Corsair', model: 'Vengeance RGB Pro', price: 299, specs: [] },
            { type: 'Motherboard', name: 'ASUS ProArt Z690', brand: 'ASUS', model: 'ProArt Z690', price: 299, specs: [] },
            { type: 'SSD', name: '2TB NVMe SSD', brand: 'Samsung', model: '980 PRO', price: 229, specs: [] },
            { type: 'PSU', name: '850W 80+ Gold', brand: 'Corsair', model: 'RM850x', price: 139, specs: [] },
            { type: 'Case', name: 'Fractal Design Define 7', brand: 'Fractal Design', model: 'Define 7', price: 169, specs: [] },
            { type: 'CPU Cooler', name: 'Noctua NH-D15', brand: 'Noctua', model: 'NH-D15', price: 99, specs: [] },
          ],
          compatibility: { isCompatible: true, issues: [] }
        }
      ]
    };
  }

  /**
   * Generate pre-configured builds using Vertex AI
   * @param forceRefresh Force refresh the cache
   * @returns Record of pre-configured builds by category
   */
  static async generatePreConfiguredBuilds(forceRefresh = false): Promise<RecommendedBuilds> {
    const currentTime = Date.now();

    // Check if we have cached data and it's still valid
    if (!forceRefresh &&
      recommendedBuildsCache !== null &&
      currentTime - lastFetchTime < CACHE_EXPIRY_TIME) {
      console.log("Using cached recommended builds");
      return recommendedBuildsCache;
    }

    try {
      // Check if Gemini AI is initialized
      if (!ai) {
        console.error("Gemini AI is not initialized");
        const defaultBuilds = this.getDefaultPreConfiguredBuilds();
        recommendedBuildsCache = defaultBuilds;
        lastFetchTime = currentTime;
        return defaultBuilds;
      }

      // Define the schema for multiple builds
      const multipleBuildsSchema: Schema = {
        type: Type.OBJECT,
        properties: {
          gaming: {
            type: Type.ARRAY,
            items: pcBuildSchema,
          },
          work: {
            type: Type.ARRAY,
            items: pcBuildSchema,
          },
          content: {
            type: Type.ARRAY,
            items: pcBuildSchema,
          },
        },
        required: ["gaming", "work", "content"]
      };

      // Construct a detailed prompt for generating multiple builds
      const prompt = `
Generate pre-configured PC builds for the following categories and budget ranges:
1. Gaming:
   - Entry-level ($500-$800)
   - Mid-range ($800-$1500)
   - High-end ($1500-$2500)

2. Work:
   - Entry-level ($500-$800)
   - Mid-range ($800-$1500)
   - High-end ($1500-$2000)

3. Content Creation:
   - Entry-level ($800-$1200)
   - Mid-range ($1200-$2000)
   - High-end ($2000-$3000)

For each build, provide:
- Name
- Description
- Total price
- Use case
- Budget range
- List of components (CPU, GPU, RAM, Motherboard, SSD, HDD, PSU, Case, CPU Cooler if needed)
- For each component, include type, name, brand, model, price, and basic specs

Ensure all components are compatible with each other and the total price falls within the specified budget range.
Response MUST have a total of 9 builds (3 builds of Content Creation, 3 builds of Gaming, and 3 builds of Work).
Format your response as structured JSON that I can parse programmatically.
Make sure the JSON is valid and does not contain any line breaks within property values.
Use a compact format without extra whitespace or newlines between properties.
`;

      const systemPrompt = `You are DIYPC, an AI assistant specialized in helping users build custom PCs.
Your primary role is to help users create PC builds based on their requirements.
You will be provided with a detailed prompt that includes the user's needs, such as the primary use case, budget, and any additional requirements.
Your task is to generate a complete PC build recommendation that meets all the specified requirements.
You will need to consider the user's budget and requirements to ensure the PC meets their needs.
You will also need to consider compatibility between components to ensure they work together.
`;

      // Call the model with the prompt and schema
      const result = await ai.models.generateContent({
        model: "gemini-2.5-flash-preview-05-20",
        contents: prompt,
        config: {
          systemInstruction: {
            role: "system",
            parts: [{ text: systemPrompt }]
          },
          responseMimeType: "application/json",
          responseSchema: multipleBuildsSchema,
          temperature: 0.4,
          maxOutputTokens: 12000,
          thinkingConfig: {
            includeThoughts: false,
            thinkingBudget: 0,
          }
        }
      });
      const responseText = result.text;

      // Parse the JSON response
      if (responseText) {
        const builds = JSON.parse(responseText) as RecommendedBuilds;

        // Validate the structure
        if (builds.gaming && builds.content && builds.work &&
          Array.isArray(builds.gaming) && Array.isArray(builds.content) && Array.isArray(builds.work)) {

          // Update cache
          recommendedBuildsCache = builds;
          lastFetchTime = currentTime;

          return builds;
        }
      }

      // If structure is invalid, fall back to default builds
      console.error("Invalid structure in model response");
      const defaultBuilds = this.getDefaultPreConfiguredBuilds();
      recommendedBuildsCache = defaultBuilds;
      lastFetchTime = currentTime;
      return defaultBuilds;
    } catch (error) {
      console.error("Error generating pre-configured builds:", error);
      const defaultBuilds = this.getDefaultPreConfiguredBuilds();
      recommendedBuildsCache = defaultBuilds;
      lastFetchTime = currentTime;
      return defaultBuilds;
    }
  }
}

export default VertexAIService;

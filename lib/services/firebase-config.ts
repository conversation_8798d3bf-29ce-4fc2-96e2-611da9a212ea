'use client';

import { initializeApp } from "firebase/app";
import { initializeAppCheck, ReCaptchaV3Provider } from "firebase/app-check";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY as string,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN as string,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID as string,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET as string,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID as string,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID as string,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID as string
};

// Initialize Firebase
export const app = initializeApp(firebaseConfig);

// Initialize App Check (only in browser environment)
if (typeof window !== 'undefined') {
  // Initialize App Check (for production, replace with your actual reCAPTCHA site key)
  // For development, we're using a debug token
  if (process.env.NODE_ENV === 'production') {
    initializeAppCheck(app, {
      provider: new ReCaptchaV3Provider(process.env.NEXT_PUBLIC_FIREBASE_RECAPTCHA_SITE_KEY as string),
      isTokenAutoRefreshEnabled: true
    });
  }
}

// else {
//   // For development, we can use debug tokens
//   // Make sure to register the debug provider in the Firebase console
//   self.FIREBASE_APPCHECK_DEBUG_TOKEN = true;
// }

export default app;

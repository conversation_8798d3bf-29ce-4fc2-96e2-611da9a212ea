'use client';

import {
  getFirestore,
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  query,
  where,
  orderBy,
  // limit,
  updateDoc,
  deleteDoc,
  DocumentData,
  QueryConstraint,
  Timestamp,
  WhereFilterOp
} from "firebase/firestore";
import { app } from "./firebase-config";
import { PCBuild } from "@/types/pc-build";

// Initialize Firestore
const db = getFirestore(app);

/**
 * Firebase Firestore Service
 * Provides methods for database operations
 */
export class FirestoreService {
  /**
   * Save a PC build to Firestore
   * @param userId User ID
   * @param buildData Build data
   * @returns Promise with saved build data including ID
   */
  static async saveBuild(userId: string, buildData: PCBuild) {
    try {
      const docRef = await addDoc(collection(db, "builds"), {
        userId,
        ...buildData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return { id: docRef.id, ...buildData };
    } catch (error) {
      console.error("Error saving build:", error);
      throw error;
    }
  }

  /**
   * Get all builds for a user
   * @param userId User ID
   * @returns Promise with array of builds
   */
  static async getUserBuilds(userId: string) {
    try {
      const q = query(
        collection(db, "builds"),
        where("userId", "==", userId),
        orderBy("updatedAt", "desc")
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error("Error getting user builds:", error);
      throw error;
    }
  }

  /**
   * Get a specific build by ID
   * @param buildId Build ID
   * @returns Promise with build data
   */
  static async getBuild(buildId: string) {
    try {
      const docRef = doc(db, "builds", buildId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      } else {
        throw new Error("Build not found");
      }
    } catch (error) {
      console.error("Error getting build:", error);
      throw error;
    }
  }

  /**
   * Update an existing build
   * @param buildId Build ID
   * @param buildData Updated build data
   * @returns Promise with updated build data
   */
  static async updateBuild(buildId: string, buildData: Partial<PCBuild>) {
    try {
      const buildRef = doc(db, "builds", buildId);
      await updateDoc(buildRef, {
        ...buildData,
        updatedAt: Timestamp.now()
      });
      return { id: buildId, ...buildData };
    } catch (error) {
      console.error("Error updating build:", error);
      throw error;
    }
  }

  /**
   * Delete a build
   * @param buildId Build ID
   * @returns Promise that resolves when deletion is complete
   */
  static async deleteBuild(buildId: string) {
    try {
      await deleteDoc(doc(db, "builds", buildId));
      return true;
    } catch (error) {
      console.error("Error deleting build:", error);
      throw error;
    }
  }

  /**
   * Generic method to add a document to any collection
   * @param collectionName Collection name
   * @param data Document data
   * @returns Promise with document reference
   */
  static async addDocument(collectionName: string, data: DocumentData) {
    try {
      return await addDoc(collection(db, collectionName), {
        ...data,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error(`Error adding document to ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Generic method to update a document in any collection
   * @param collectionName Collection name
   * @param documentId Document ID
   * @param data Document data to update
   * @returns Promise that resolves when update is complete
   */
  static async updateDocument(collectionName: string, documentId: string, data: Partial<DocumentData>) {
    try {
      const docRef = doc(db, collectionName, documentId);
      await updateDoc(docRef, {
        ...data,
        updatedAt: Timestamp.now()
      });
      return { id: documentId, ...data };
    } catch (error) {
      console.error(`Error updating document in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Generic method to get a document from any collection
   * @param collectionName Collection name
   * @param documentId Document ID
   * @returns Promise with document data
   */
  static async getDocument(collectionName: string, documentId: string) {
    try {
      const docRef = doc(db, collectionName, documentId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      } else {
        throw new Error(`Document not found in ${collectionName}`);
      }
    } catch (error) {
      console.error(`Error getting document from ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Generic method to query documents from any collection
   * @param collectionName Collection name
   * @param constraints Query constraints or where clauses
   * @returns Promise with query results
   */
  static async queryDocuments(
    collectionName: string,
    constraints: QueryConstraint[] | { fieldPath: string; opStr: WhereFilterOp; value: unknown }[] = []
  ) {
    try {
      let queryConstraints: QueryConstraint[] = [];

      // Convert where clauses to query constraints if needed
      if (constraints.length > 0 && 'fieldPath' in constraints[0]) {
        const whereClauses = constraints as { fieldPath: string; opStr: WhereFilterOp; value: unknown }[];
        queryConstraints = whereClauses.map(clause =>
          where(clause.fieldPath, clause.opStr, clause.value)
        );
      } else {
        queryConstraints = constraints as QueryConstraint[];
      }

      const q = query(collection(db, collectionName), ...queryConstraints);
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error(`Error querying documents from ${collectionName}:`, error);
      throw error;
    }
  }
}

export default FirestoreService;
